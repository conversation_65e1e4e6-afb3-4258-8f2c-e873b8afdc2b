#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统状态检查器 - 检查智能系统的运行状态
"""

import os
import sqlite3
import json
import datetime

def check_system_status():
    """检查系统状态"""
    status = {
        'timestamp': datetime.datetime.now().isoformat(),
        'databases': {},
        'files': {},
        'overall_status': 'healthy'
    }
    
    # 检查数据库
    databases = [
        'email_receiver.db',
        'recipient_quality.db', 
        'intelligent_recall.db'
    ]
    
    for db in databases:
        if os.path.exists(db):
            try:
                conn = sqlite3.connect(db, timeout=10.0)
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
                table_count = cursor.fetchone()[0]
                conn.close()
                
                status['databases'][db] = {
                    'exists': True,
                    'table_count': table_count,
                    'status': 'ok'
                }
            except Exception as e:
                status['databases'][db] = {
                    'exists': True,
                    'error': str(e),
                    'status': 'error'
                }
                status['overall_status'] = 'warning'
        else:
            status['databases'][db] = {
                'exists': False,
                'status': 'missing'
            }
    
    # 检查关键文件
    files = [
        'email_receiver.py',
        'intelligent_recall_manager.py',
        'status_update_fixer.py',
        'auth_codes.json'
    ]
    
    for file in files:
        status['files'][file] = {
            'exists': os.path.exists(file),
            'size': os.path.getsize(file) if os.path.exists(file) else 0
        }
    
    return status

if __name__ == "__main__":
    status = check_system_status()
    print(json.dumps(status, ensure_ascii=False, indent=2))

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复占位符文本不一致问题
确保所有地方使用相同的占位符文本，避免空正文被误判
"""

import re
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_placeholder_consistency():
    """修复占位符文本一致性"""
    logger.info("🔧 开始修复占位符文本一致性...")
    
    try:
        # 统一的占位符文本
        UNIFIED_PLACEHOLDER = "请在此输入邮件正文内容...\n\n支持功能：\n• 支持中文输入\n• 支持Emoji表情 😊\n• 支持多行文本\n• 右键菜单快速插入表情"
        
        # 读取文件
        with open('gui_main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找所有占位符文本的定义
        placeholder_patterns = [
            # 简化版本
            r'placeholder_text = "请在此输入邮件正文内容\.\.\.\n\n支持中文、Emoji表情 😊"',
            # 其他可能的变体
            r'placeholder_text = "请在此输入邮件正文内容\.\.\.\n\n支持中文.*?"',
        ]
        
        # 替换所有不一致的占位符文本
        changes_made = 0
        for pattern in placeholder_patterns:
            matches = re.findall(pattern, content, re.DOTALL)
            if matches:
                logger.info(f"发现 {len(matches)} 个需要修复的占位符文本")
                content = re.sub(pattern, f'placeholder_text = "{UNIFIED_PLACEHOLDER}"', content, flags=re.DOTALL)
                changes_made += len(matches)
        
        # 创建一个统一的占位符文本常量
        placeholder_constant = '''
    # 统一的占位符文本常量
    PLACEHOLDER_TEXT = "请在此输入邮件正文内容...\\n\\n支持功能：\\n• 支持中文输入\\n• 支持Emoji表情 😊\\n• 支持多行文本\\n• 右键菜单快速插入表情"
'''
        
        # 在类定义开始处添加常量（如果还没有的话）
        if 'PLACEHOLDER_TEXT =' not in content:
            # 找到类定义的位置
            class_match = re.search(r'class EmailSenderGUI:', content)
            if class_match:
                insert_pos = class_match.end()
                content = content[:insert_pos] + placeholder_constant + content[insert_pos:]
                logger.info("✅ 添加了统一的占位符文本常量")
                changes_made += 1
        
        # 替换所有硬编码的占位符文本为常量引用
        hardcoded_patterns = [
            r'"请在此输入邮件正文内容\.\.\.\n\n支持功能：.*?"',
        ]
        
        for pattern in hardcoded_patterns:
            if re.search(pattern, content, re.DOTALL):
                content = re.sub(pattern, 'self.PLACEHOLDER_TEXT', content, flags=re.DOTALL)
                changes_made += 1
                logger.info("✅ 替换硬编码占位符文本为常量引用")
        
        # 写回文件
        if changes_made > 0:
            with open('gui_main.py', 'w', encoding='utf-8') as f:
                f.write(content)
            logger.info(f"✅ 修复完成，共进行了 {changes_made} 处修改")
        else:
            logger.info("ℹ️ 未发现需要修复的占位符文本")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 修复占位符文本失败: {str(e)}")
        return False

def create_empty_body_validator():
    """创建空正文验证器"""
    logger.info("🔧 创建空正文验证器...")
    
    try:
        validator_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
空正文验证器 - 确保空正文不被添加默认内容
"""

class EmptyBodyValidator:
    """空正文验证器"""
    
    # 统一的占位符文本
    PLACEHOLDER_TEXT = "请在此输入邮件正文内容...\\n\\n支持功能：\\n• 支持中文输入\\n• 支持Emoji表情 😊\\n• 支持多行文本\\n• 右键菜单快速插入表情"
    
    @classmethod
    def is_empty_body(cls, body_text):
        """判断是否为空正文"""
        if not body_text:
            return True
        
        # 去除空白字符
        cleaned_text = body_text.strip()
        if not cleaned_text:
            return True
        
        # 检查是否为占位符文本
        if cleaned_text == cls.PLACEHOLDER_TEXT.strip():
            return True
        
        return False
    
    @classmethod
    def process_body_text(cls, body_text):
        """处理正文文本，确保空正文返回空字符串"""
        if cls.is_empty_body(body_text):
            return ""
        return body_text.strip()
    
    @classmethod
    def validate_email_body(cls, body_text):
        """验证邮件正文，返回处理后的正文和是否为空的标志"""
        processed_body = cls.process_body_text(body_text)
        is_empty = len(processed_body) == 0
        
        return processed_body, is_empty

# 全局验证器实例
validator = EmptyBodyValidator()

def validate_body(body_text):
    """全局验证函数"""
    return validator.validate_email_body(body_text)

def is_empty_body(body_text):
    """全局判断函数"""
    return validator.is_empty_body(body_text)

def process_body(body_text):
    """全局处理函数"""
    return validator.process_body_text(body_text)
'''
        
        with open('empty_body_validator.py', 'w', encoding='utf-8') as f:
            f.write(validator_code)
        
        logger.info("✅ 空正文验证器创建完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 创建空正文验证器失败: {str(e)}")
        return False

def test_empty_body_fix():
    """测试空正文修复"""
    logger.info("🧪 测试空正文修复...")
    
    try:
        from empty_body_validator import validate_body, is_empty_body, process_body
        
        test_cases = [
            ("", "完全空正文"),
            ("   \n\t  \n  ", "空白字符正文"),
            ("请在此输入邮件正文内容...\n\n支持功能：\n• 支持中文输入\n• 支持Emoji表情 😊\n• 支持多行文本\n• 右键菜单快速插入表情", "占位符文本"),
            ("这是真实的邮件内容", "真实内容")
        ]
        
        logger.info("📊 测试结果:")
        for body_text, description in test_cases:
            processed_body, is_empty = validate_body(body_text)
            
            logger.info(f"  {description}:")
            logger.info(f"    原始长度: {len(body_text)}")
            logger.info(f"    处理后长度: {len(processed_body)}")
            logger.info(f"    是否为空: {is_empty}")
            logger.info(f"    处理结果: {'✅ 正确' if (is_empty and len(processed_body) == 0) or (not is_empty and len(processed_body) > 0) else '❌ 错误'}")
            logger.info("")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试空正文修复失败: {str(e)}")
        return False

def main():
    """主修复函数"""
    logger.info("🚀 开始修复空正文问题...")
    
    try:
        # 1. 修复占位符文本一致性
        placeholder_success = fix_placeholder_consistency()
        
        # 2. 创建空正文验证器
        validator_success = create_empty_body_validator()
        
        # 3. 测试修复效果
        test_success = test_empty_body_fix()
        
        if placeholder_success and validator_success and test_success:
            logger.info("\n🎉 空正文问题修复完成！")
            logger.info("✅ 占位符文本已统一")
            logger.info("✅ 空正文验证器已创建")
            logger.info("✅ 测试验证通过")
            
            logger.info("\n💡 使用建议:")
            logger.info("1. 重启GUI程序以应用修复")
            logger.info("2. 测试空正文发送功能")
            logger.info("3. 确认不会添加默认内容")
            
            return True
        else:
            logger.warning("\n⚠️ 部分修复失败")
            return False
        
    except Exception as e:
        logger.error(f"❌ 修复失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能改进功能测试脚本
测试自动回复智能判断、撤回功能和状态更新机制
"""

import logging
import datetime
import json
import os
import sys

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('intelligent_improvements_test.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def test_intelligent_auto_reply_detection():
    """测试智能自动回复检测"""
    logger.info("🧪 开始测试智能自动回复检测...")
    
    try:
        from email_receiver import EmailReceiver
        
        # 创建测试接收器
        receiver = EmailReceiver("<EMAIL>", "test_password")
        
        # 测试邮件内容
        test_emails = [
            {
                'subject': '自动回复：暂停收稿',
                'body': '您好，我们目前暂停收稿，请勿继续发送。',
                'from': '<EMAIL>'
            },
            {
                'subject': 'Re: 投稿申请',
                'body': '谢谢您的投稿，但我们已停止收稿，请不要再发送了。',
                'from': '<EMAIL>'
            },
            {
                'subject': 'Auto Reply',
                'body': 'Thank you for your email. I am currently out of office.',
                'from': '<EMAIL>'
            },
            {
                'subject': 'Delivery Failure',
                'body': 'User unknown. Mail delivery failed.',
                'from': '<EMAIL>'
            },
            {
                'subject': '普通回复',
                'body': '您好，我已收到您的邮件，会尽快回复。',
                'from': '<EMAIL>'
            }
        ]
        
        results = []
        for i, email_content in enumerate(test_emails, 1):
            is_auto, reply_type = receiver.is_auto_reply(email_content)
            
            result = {
                'test_case': i,
                'subject': email_content['subject'],
                'is_auto_reply': is_auto,
                'reply_type': reply_type,
                'expected_type': ['rejection', 'rejection', 'auto_reply', 'bounce', 'normal'][i-1]
            }
            results.append(result)
            
            status = "✅" if reply_type == result['expected_type'] else "❌"
            logger.info(f"{status} 测试用例 {i}: {email_content['subject']} -> {reply_type}")
        
        # 统计结果
        correct_count = sum(1 for r in results if r['reply_type'] == r['expected_type'])
        total_count = len(results)
        accuracy = correct_count / total_count * 100
        
        logger.info(f"📊 智能检测准确率: {correct_count}/{total_count} ({accuracy:.1f}%)")
        
        return accuracy >= 80  # 80%以上准确率算通过
        
    except Exception as e:
        logger.error(f"❌ 智能自动回复检测测试失败: {str(e)}")
        return False

def test_intelligent_recall_manager():
    """测试智能撤回管理器"""
    logger.info("🧪 开始测试智能撤回管理器...")
    
    try:
        from intelligent_recall_manager import get_recall_manager
        
        recall_manager = get_recall_manager()
        
        # 测试添加撤回任务
        test_recipients = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ]
        
        success_count = 0
        for recipient in test_recipients:
            success = recall_manager.add_recall_task(
                recipient_email=recipient,
                sender_email='<EMAIL>',
                trigger_reason='测试拒绝关键词检测',
                custom_template={
                    'subject': '测试撤回邮件',
                    'body': '这是一封测试撤回邮件，请忽略。'
                }
            )
            
            if success:
                success_count += 1
                logger.info(f"✅ 撤回任务创建成功: {recipient}")
            else:
                logger.error(f"❌ 撤回任务创建失败: {recipient}")
        
        # 获取统计信息
        stats = recall_manager.get_recall_statistics('<EMAIL>')
        logger.info(f"📊 撤回统计: {json.dumps(stats, ensure_ascii=False, indent=2)}")
        
        return success_count == len(test_recipients)
        
    except Exception as e:
        logger.error(f"❌ 智能撤回管理器测试失败: {str(e)}")
        return False

def test_status_update_fixer():
    """测试状态更新修复器"""
    logger.info("🧪 开始测试状态更新修复器...")
    
    try:
        from status_update_fixer import get_status_fixer
        
        status_fixer = get_status_fixer()
        
        # 测试状态更新
        test_updates = [
            ('<EMAIL>', '<EMAIL>', 'active', 'test', '测试活跃状态'),
            ('<EMAIL>', '<EMAIL>', 'invalid', 'test', '测试无效状态'),
            ('<EMAIL>', '<EMAIL>', 'rejected', 'test', '测试拒绝状态'),
        ]
        
        success_count = 0
        for recipient, sender, status, source, notes in test_updates:
            success = status_fixer.update_recipient_status_safe(
                recipient, sender, status, source, notes
            )
            
            if success:
                success_count += 1
                logger.info(f"✅ 状态更新成功: {recipient} -> {status}")
            else:
                logger.error(f"❌ 状态更新失败: {recipient}")
        
        # 获取统计信息
        stats = status_fixer.get_status_statistics()
        logger.info(f"📊 状态统计: {json.dumps(stats, ensure_ascii=False, indent=2)}")
        
        return success_count == len(test_updates)
        
    except Exception as e:
        logger.error(f"❌ 状态更新修复器测试失败: {str(e)}")
        return False

def test_recall_email_functionality():
    """测试撤回邮件功能"""
    logger.info("🧪 开始测试撤回邮件功能...")
    
    try:
        # 检查撤回功能是否可用
        from email_sender import EmailSender
        
        # 模拟测试（不实际发送）
        test_sender = "<EMAIL>"
        test_recipients = ["<EMAIL>", "<EMAIL>"]
        
        # 检查是否能创建发送器实例
        try:
            sender = EmailSender(test_sender)
            logger.info("✅ 邮件发送器创建成功")
            
            # 模拟撤回邮件发送（不实际发送）
            recall_subject = "测试撤回邮件"
            recall_body = "这是一封测试撤回邮件，请忽略之前的邮件。"
            
            logger.info(f"📤 模拟撤回邮件发送:")
            logger.info(f"   收件人: {', '.join(test_recipients)}")
            logger.info(f"   主题: {recall_subject}")
            logger.info(f"   内容长度: {len(recall_body)} 字符")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 邮件发送器创建失败: {str(e)}")
            return False
        
    except Exception as e:
        logger.error(f"❌ 撤回邮件功能测试失败: {str(e)}")
        return False

def test_integration_workflow():
    """测试集成工作流程"""
    logger.info("🧪 开始测试集成工作流程...")
    
    try:
        # 模拟完整的智能处理流程
        logger.info("1️⃣ 模拟接收到拒绝回复...")
        
        # 模拟邮件内容
        rejection_email = {
            'subject': '自动回复：请停止发送',
            'body': '您好，我们已暂停收稿，请不要再发送邮件了。谢谢！',
            'from': '<EMAIL>',
            'reply_time': datetime.datetime.now().isoformat()
        }
        
        # 测试智能检测
        from email_receiver import EmailReceiver
        receiver = EmailReceiver("<EMAIL>", "test_password")
        
        is_auto, reply_type = receiver.is_auto_reply(rejection_email)
        logger.info(f"2️⃣ 智能检测结果: {reply_type} (是否自动回复: {is_auto})")
        
        if reply_type == 'rejection':
            logger.info("3️⃣ 检测到拒绝关键词，触发智能处理...")
            
            # 模拟触发撤回
            recipient_email = "<EMAIL>"
            receiver._trigger_auto_recall(recipient_email, rejection_email)
            
            logger.info("4️⃣ 自动撤回流程已触发")
            
            # 模拟状态更新
            from status_update_fixer import get_status_fixer
            status_fixer = get_status_fixer()
            
            success = status_fixer.update_recipient_status_safe(
                recipient_email, "<EMAIL>", 'rejected', 
                'auto_reply', '检测到拒绝关键词'
            )
            
            if success:
                logger.info("5️⃣ 收件人状态更新成功")
            else:
                logger.warning("5️⃣ 收件人状态更新失败")
            
            logger.info("✅ 集成工作流程测试完成")
            return True
        else:
            logger.warning("❌ 未能正确检测到拒绝类型")
            return False
        
    except Exception as e:
        logger.error(f"❌ 集成工作流程测试失败: {str(e)}")
        return False

def generate_test_report(test_results):
    """生成测试报告"""
    logger.info("📋 生成测试报告...")
    
    try:
        report = {
            'test_time': datetime.datetime.now().isoformat(),
            'test_results': test_results,
            'summary': {
                'total_tests': len(test_results),
                'passed_tests': sum(1 for r in test_results.values() if r),
                'failed_tests': sum(1 for r in test_results.values() if not r),
                'success_rate': sum(1 for r in test_results.values() if r) / len(test_results) * 100
            },
            'recommendations': []
        }
        
        # 添加建议
        if not test_results.get('intelligent_detection', False):
            report['recommendations'].append("需要优化智能自动回复检测算法")
        
        if not test_results.get('recall_manager', False):
            report['recommendations'].append("需要修复智能撤回管理器")
        
        if not test_results.get('status_fixer', False):
            report['recommendations'].append("需要修复状态更新机制")
        
        if not test_results.get('recall_functionality', False):
            report['recommendations'].append("需要检查撤回邮件发送功能")
        
        if not test_results.get('integration_workflow', False):
            report['recommendations'].append("需要优化集成工作流程")
        
        # 保存报告
        report_file = f'intelligent_improvements_test_report_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📄 测试报告已保存: {report_file}")
        
        # 打印摘要
        logger.info("📊 测试摘要:")
        logger.info(f"   总测试数: {report['summary']['total_tests']}")
        logger.info(f"   通过测试: {report['summary']['passed_tests']}")
        logger.info(f"   失败测试: {report['summary']['failed_tests']}")
        logger.info(f"   成功率: {report['summary']['success_rate']:.1f}%")
        
        if report['recommendations']:
            logger.info("💡 改进建议:")
            for i, rec in enumerate(report['recommendations'], 1):
                logger.info(f"   {i}. {rec}")
        
        return report
        
    except Exception as e:
        logger.error(f"❌ 生成测试报告失败: {str(e)}")
        return None

def main():
    """主测试函数"""
    logger.info("🚀 开始智能改进功能测试...")
    
    # 执行所有测试
    test_results = {}
    
    try:
        test_results['intelligent_detection'] = test_intelligent_auto_reply_detection()
        test_results['recall_manager'] = test_intelligent_recall_manager()
        test_results['status_fixer'] = test_status_update_fixer()
        test_results['recall_functionality'] = test_recall_email_functionality()
        test_results['integration_workflow'] = test_integration_workflow()
        
        # 生成测试报告
        report = generate_test_report(test_results)
        
        # 总结
        passed_count = sum(1 for r in test_results.values() if r)
        total_count = len(test_results)
        
        if passed_count == total_count:
            logger.info("🎉 所有测试通过！智能改进功能正常工作")
            return True
        else:
            logger.warning(f"⚠️ 部分测试失败 ({passed_count}/{total_count})，需要进一步修复")
            return False
        
    except Exception as e:
        logger.error(f"❌ 测试执行失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

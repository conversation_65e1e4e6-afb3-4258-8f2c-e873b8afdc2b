#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终极语法修复器
彻底修复所有语法错误
"""

import re
import os
import sys
import logging
import subprocess
import shutil
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UltimateSyntaxFixer:
    """终极语法修复器"""
    
    def __init__(self):
        self.main_file = "gui_main.py"
        self.backup_file = f"gui_main_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py"
        self.fixes_applied = []
    
    def create_backup(self):
        """创建备份"""
        try:
            shutil.copy2(self.main_file, self.backup_file)
            logger.info(f"✅ 已创建备份: {self.backup_file}")
            return True
        except Exception as e:
            logger.error(f"❌ 创建备份失败: {str(e)}")
            return False
    
    def fix_all_syntax_errors(self):
        """修复所有语法错误"""
        logger.info("🔧 开始终极语法修复...")
        
        try:
            with open(self.main_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 1. 修复不完整的try块
            content = self._fix_incomplete_try_blocks(content)
            
            # 2. 修复缩进错误
            content = self._fix_indentation_errors(content)
            
            # 3. 修复语法结构错误
            content = self._fix_syntax_structure_errors(content)
            
            # 4. 修复特定的空正文验证器问题
            content = self._fix_empty_body_validator_issues(content)
            
            # 5. 修复函数定义问题
            content = self._fix_function_definition_issues(content)
            
            # 6. 最终清理
            content = self._final_cleanup(content)
            
            if content != original_content:
                with open(self.main_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                logger.info(f"✅ 应用了 {len(self.fixes_applied)} 个修复")
                for fix in self.fixes_applied:
                    logger.info(f"   • {fix}")
                
                return True
            else:
                logger.info("ℹ️ 未发现需要修复的语法错误")
                return True
                
        except Exception as e:
            logger.error(f"❌ 终极语法修复失败: {str(e)}")
            return False
    
    def _fix_incomplete_try_blocks(self, content):
        """修复不完整的try块"""
        logger.info("🔧 修复不完整的try块...")
        
        # 模式1: try块后直接跟注释和另一个try
        pattern1 = r'(\s+try:\s*\n(?:\s+[^#\n][^\n]*\n)*?)\s+# 使用统一的空正文验证器\s*\n\s+try:'
        
        def fix_pattern1(match):
            original = match.group(0)
            # 在第一个try块后添加except
            try_block = match.group(1)
            indent_match = re.search(r'^(\s+)try:', try_block, re.MULTILINE)
            if indent_match:
                indent = indent_match.group(1)
                except_block = f"\n{indent}except Exception as e:\n{indent}    pass\n\n{indent}# 使用统一的空正文验证器\n{indent}try:"
                self.fixes_applied.append("修复不完整try块(模式1)")
                return try_block.rstrip() + except_block
            return original
        
        content = re.sub(pattern1, fix_pattern1, content, flags=re.MULTILINE)
        
        # 模式2: 孤立的try块
        pattern2 = r'(\s+try:\s*\n(?:\s+[^#\n][^\n]*\n)*?)(\s+def\s+|\s+class\s+|\s+@|\Z)'
        
        def fix_pattern2(match):
            try_block = match.group(1)
            next_part = match.group(2)
            
            # 检查是否已有except或finally
            if 'except' not in try_block and 'finally' not in try_block:
                indent_match = re.search(r'^(\s+)try:', try_block, re.MULTILINE)
                if indent_match:
                    indent = indent_match.group(1)
                    except_block = f"\n{indent}except Exception as e:\n{indent}    logger.error(f'Error: {{str(e)}}')\n{indent}    pass\n"
                    self.fixes_applied.append("修复孤立try块(模式2)")
                    return try_block.rstrip() + except_block + "\n" + next_part
            
            return match.group(0)
        
        content = re.sub(pattern2, fix_pattern2, content, flags=re.MULTILINE | re.DOTALL)
        
        return content
    
    def _fix_indentation_errors(self, content):
        """修复缩进错误"""
        logger.info("🔧 修复缩进错误...")
        
        lines = content.split('\n')
        fixed_lines = []
        
        for i, line in enumerate(lines):
            # 检查常见的缩进问题
            if line.strip().startswith('if not all([') and i > 0:
                # 确保if语句有正确的缩进
                prev_line = lines[i-1]
                if 'except ImportError:' in prev_line:
                    # 获取except的缩进级别
                    except_indent = len(prev_line) - len(prev_line.lstrip())
                    correct_indent = ' ' * (except_indent + 4)
                    if not line.startswith(correct_indent):
                        line = correct_indent + line.lstrip()
                        self.fixes_applied.append(f"修复缩进错误(第{i+1}行)")
            
            fixed_lines.append(line)
        
        return '\n'.join(fixed_lines)
    
    def _fix_syntax_structure_errors(self, content):
        """修复语法结构错误"""
        logger.info("🔧 修复语法结构错误...")
        
        # 修复意外缩进
        lines = content.split('\n')
        fixed_lines = []
        
        for i, line in enumerate(lines):
            # 检查意外缩进
            if line.strip() and i > 0:
                prev_line = lines[i-1].strip()
                
                # 如果前一行是except或其他块结束，当前行不应该有额外缩进
                if (prev_line.endswith(':') or 
                    prev_line.startswith('except ') or 
                    prev_line.startswith('finally:') or
                    prev_line == ''):
                    
                    # 检查当前行是否应该减少缩进
                    if (line.strip().startswith('if not all([') or
                        line.strip().startswith('messagebox.') or
                        line.strip().startswith('return') or
                        line.strip().startswith('self.log_message')):
                        
                        # 找到合适的缩进级别
                        for j in range(i-1, -1, -1):
                            if lines[j].strip() and not lines[j].strip().startswith('#'):
                                if ('def ' in lines[j] or 'try:' in lines[j]):
                                    base_indent = len(lines[j]) - len(lines[j].lstrip())
                                    correct_indent = ' ' * (base_indent + 4)
                                    if not line.startswith(correct_indent):
                                        line = correct_indent + line.lstrip()
                                        self.fixes_applied.append(f"修复意外缩进(第{i+1}行)")
                                    break
            
            fixed_lines.append(line)
        
        return '\n'.join(fixed_lines)
    
    def _fix_empty_body_validator_issues(self, content):
        """修复空正文验证器问题"""
        logger.info("🔧 修复空正文验证器问题...")
        
        # 移除重复的空正文验证器代码
        pattern = r'(\s+# 使用统一的空正文验证器\s*\n\s+try:\s*\n\s+from empty_body_validator import process_body\s*\n\s+body = process_body\(body_raw\)\s*\n\s+except ImportError:\s*\n\s+# 回退到原有逻辑\s*\n\s+if body_raw == self\.PLACEHOLDER_TEXT\.strip\(\):\s*\n\s+body = ""\s*\n\s+else:\s*\n\s+body = body_raw\.strip\(\))\s*\n\s+# 使用统一的空正文验证器\s*\n\s+try:\s*\n\s+from empty_body_validator import process_body\s*\n\s+body = process_body\(body\)\s*\n\s+except ImportError:\s*\n\s+# 回退到原有逻辑\s*\n\s+if body == self\.PLACEHOLDER_TEXT\.strip\(\):\s*\n\s+body = ""'
        
        def fix_duplicate_validator(match):
            # 只保留第一个验证器
            self.fixes_applied.append("移除重复的空正文验证器")
            return match.group(1)
        
        content = re.sub(pattern, fix_duplicate_validator, content, flags=re.MULTILINE)
        
        return content
    
    def _fix_function_definition_issues(self, content):
        """修复函数定义问题"""
        logger.info("🔧 修复函数定义问题...")
        
        # 确保函数定义后有正确的缩进
        lines = content.split('\n')
        fixed_lines = []
        
        for i, line in enumerate(lines):
            if line.strip().startswith('def ') and ':' in line:
                # 函数定义行
                fixed_lines.append(line)
                
                # 检查下一行是否有正确的缩进
                if i + 1 < len(lines):
                    next_line = lines[i + 1]
                    if next_line.strip() and not next_line.startswith('    '):
                        # 如果下一行不是空行且没有正确缩进，修复它
                        func_indent = len(line) - len(line.lstrip())
                        correct_indent = ' ' * (func_indent + 4)
                        if not next_line.startswith(correct_indent):
                            lines[i + 1] = correct_indent + next_line.lstrip()
                            self.fixes_applied.append(f"修复函数定义后的缩进(第{i+2}行)")
            else:
                fixed_lines.append(line)
        
        return '\n'.join(lines)
    
    def _final_cleanup(self, content):
        """最终清理"""
        logger.info("🔧 最终清理...")
        
        # 移除多余的空行
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
        
        # 确保文件以换行符结尾
        if not content.endswith('\n'):
            content += '\n'
        
        self.fixes_applied.append("最终清理")
        return content
    
    def test_syntax(self):
        """测试语法"""
        logger.info("🧪 测试语法...")
        
        try:
            result = subprocess.run([sys.executable, '-m', 'py_compile', self.main_file], 
                                  capture_output=True, text=True, encoding='utf-8')
            
            if result.returncode == 0:
                logger.info("✅ 语法检查通过")
                return True, None
            else:
                logger.error(f"❌ 语法检查失败: {result.stderr}")
                return False, result.stderr
                
        except Exception as e:
            logger.error(f"❌ 语法测试失败: {str(e)}")
            return False, str(e)
    
    def run_ultimate_fix(self):
        """运行终极修复"""
        logger.info("🚀 开始终极语法修复...")
        
        try:
            # 1. 创建备份
            if not self.create_backup():
                return False
            
            # 2. 修复所有语法错误
            if not self.fix_all_syntax_errors():
                return False
            
            # 3. 测试语法
            syntax_ok, error_msg = self.test_syntax()
            
            if syntax_ok:
                logger.info("🎉 终极语法修复成功！")
                logger.info(f"✅ 共应用了 {len(self.fixes_applied)} 个修复")
                logger.info(f"✅ 备份文件: {self.backup_file}")
                return True
            else:
                logger.warning("⚠️ 语法修复完成但仍有错误")
                logger.warning(f"错误信息: {error_msg}")
                return False
            
        except Exception as e:
            logger.error(f"❌ 终极修复失败: {str(e)}")
            return False

def main():
    """主函数"""
    print("🚀 终极语法修复器")
    print("="*50)
    
    fixer = UltimateSyntaxFixer()
    
    try:
        success = fixer.run_ultimate_fix()
        
        if success:
            print("\n🎉 终极语法修复成功！")
            print("💡 现在可以启动系统了")
        else:
            print("\n⚠️ 语法修复完成但可能仍有问题")
            print("💡 请检查错误信息并手动修复")
        
        return success
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断修复过程")
        return False
    except Exception as e:
        print(f"\n❌ 修复过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

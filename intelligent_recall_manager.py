#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能撤回管理器 - 处理自动撤回功能
当检测到拒绝关键词时自动触发撤回机制
"""

import logging
import sqlite3
import datetime
import json
import os
import threading
import time
from typing import List, Dict, Optional, Callable
from dataclasses import dataclass

@dataclass
class RecallTask:
    """撤回任务"""
    recipient_email: str
    sender_email: str
    trigger_reason: str
    trigger_time: str
    status: str = 'pending'
    recall_subject: str = '邮件撤回通知'
    recall_body: str = ''

class IntelligentRecallManager:
    """智能撤回管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.db_path = 'intelligent_recall.db'
        self.notification_file = 'auto_recall_notifications.json'
        self.recall_queue = []
        self.processing_lock = threading.RLock()
        self.is_processing = False
        
        # 初始化数据库
        self._init_database()
        
        # 默认撤回邮件模板
        self.default_recall_template = {
            'subject': '重要通知：请忽略之前的邮件',
            'body': '''尊敬的收件人：

根据您的回复，我们理解您不希望继续接收此类邮件。

请忽略我们之前发送的邮件内容，我们已将您的邮箱从发送列表中移除。

如有任何疑问，请联系我们。

谢谢您的理解！

此邮件为系统自动发送，请勿回复。'''
        }
        
        self.logger.info("智能撤回管理器初始化完成")
    
    def _init_database(self):
        """初始化数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建撤回任务表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS recall_tasks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    recipient_email TEXT NOT NULL,
                    sender_email TEXT NOT NULL,
                    trigger_reason TEXT,
                    trigger_time TEXT NOT NULL,
                    status TEXT DEFAULT 'pending',
                    recall_subject TEXT,
                    recall_body TEXT,
                    processed_time TEXT,
                    success BOOLEAN DEFAULT 0,
                    error_message TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建撤回历史表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS recall_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    recipient_email TEXT NOT NULL,
                    sender_email TEXT NOT NULL,
                    recall_time TEXT NOT NULL,
                    recall_subject TEXT,
                    recall_body TEXT,
                    success BOOLEAN DEFAULT 0,
                    error_message TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
            
            self.logger.info("✅ 智能撤回数据库初始化完成")
            
        except Exception as e:
            self.logger.error(f"❌ 初始化撤回数据库失败: {str(e)}")
    
    def add_recall_task(self, recipient_email: str, sender_email: str, 
                       trigger_reason: str, custom_template: Dict = None) -> bool:
        """添加撤回任务"""
        try:
            with self.processing_lock:
                # 使用自定义模板或默认模板
                template = custom_template or self.default_recall_template
                
                task = RecallTask(
                    recipient_email=recipient_email,
                    sender_email=sender_email,
                    trigger_reason=trigger_reason,
                    trigger_time=datetime.datetime.now().isoformat(),
                    recall_subject=template['subject'],
                    recall_body=template['body']
                )
                
                # 保存到数据库
                self._save_recall_task(task)
                
                # 添加到队列
                self.recall_queue.append(task)
                
                self.logger.info(f"✅ 撤回任务已添加: {recipient_email}")
                
                # 如果没有在处理，启动处理
                if not self.is_processing:
                    self._start_processing()
                
                return True
                
        except Exception as e:
            self.logger.error(f"❌ 添加撤回任务失败: {str(e)}")
            return False
    
    def _save_recall_task(self, task: RecallTask):
        """保存撤回任务到数据库"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO recall_tasks 
                (recipient_email, sender_email, trigger_reason, trigger_time, 
                 status, recall_subject, recall_body)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                task.recipient_email,
                task.sender_email,
                task.trigger_reason,
                task.trigger_time,
                task.status,
                task.recall_subject,
                task.recall_body
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"❌ 保存撤回任务失败: {str(e)}")
    
    def _start_processing(self):
        """启动撤回任务处理"""
        if self.is_processing:
            return
        
        self.is_processing = True
        processing_thread = threading.Thread(target=self._process_recall_queue, daemon=True)
        processing_thread.start()
        
        self.logger.info("🔄 撤回任务处理已启动")
    
    def _process_recall_queue(self):
        """处理撤回队列"""
        try:
            while self.recall_queue:
                with self.processing_lock:
                    if not self.recall_queue:
                        break
                    
                    task = self.recall_queue.pop(0)
                
                # 处理撤回任务
                success = self._execute_recall_task(task)
                
                # 更新任务状态
                self._update_task_status(task, success)
                
                # 添加延迟避免过于频繁
                time.sleep(2)
            
        except Exception as e:
            self.logger.error(f"❌ 处理撤回队列失败: {str(e)}")
        finally:
            self.is_processing = False
            self.logger.info("✅ 撤回任务处理完成")
    
    def _execute_recall_task(self, task: RecallTask) -> bool:
        """执行撤回任务"""
        try:
            self.logger.info(f"📤 执行撤回任务: {task.recipient_email}")
            
            # 尝试发送撤回邮件
            success = self._send_recall_email(task)
            
            if success:
                self.logger.info(f"✅ 撤回邮件发送成功: {task.recipient_email}")
                
                # 记录到撤回历史
                self._save_recall_history(task, True, None)
                
                # 更新收件人状态为已撤回
                self._update_recipient_status(task.recipient_email, task.sender_email, 'recalled')
                
            else:
                self.logger.error(f"❌ 撤回邮件发送失败: {task.recipient_email}")
            
            return success
            
        except Exception as e:
            error_msg = str(e)
            self.logger.error(f"❌ 执行撤回任务异常: {error_msg}")
            self._save_recall_history(task, False, error_msg)
            return False
    
    def _send_recall_email(self, task: RecallTask) -> bool:
        """发送撤回邮件"""
        try:
            # 导入邮件发送器
            from email_sender import EmailSender
            
            # 获取授权码（这里需要从配置中获取）
            auth_code = self._get_auth_code(task.sender_email)
            if not auth_code:
                self.logger.error(f"❌ 无法获取授权码: {task.sender_email}")
                return False
            
            # 创建邮件发送器
            sender = EmailSender(task.sender_email)
            sender.smtp_config['password'] = auth_code
            
            # 发送撤回邮件
            success = sender.send_email(
                to_emails=[task.recipient_email],
                subject=task.recall_subject,
                body=task.recall_body
            )
            
            return success
            
        except Exception as e:
            self.logger.error(f"❌ 发送撤回邮件失败: {str(e)}")
            return False
    
    def _get_auth_code(self, sender_email: str) -> Optional[str]:
        """获取发件人授权码"""
        try:
            # 从配置文件获取授权码
            if os.path.exists('auth_codes.json'):
                with open('auth_codes.json', 'r', encoding='utf-8') as f:
                    auth_codes = json.load(f)
                    return auth_codes.get(sender_email)
            
            return None
            
        except Exception as e:
            self.logger.error(f"❌ 获取授权码失败: {str(e)}")
            return None
    
    
    def _get_auth_code_from_gui(self, sender_email: str) -> Optional[str]:
        """从GUI配置获取授权码"""
        try:
            # 尝试从GUI的auth_codes字典获取
            import gui_main
            if hasattr(gui_main, 'auth_codes') and sender_email in gui_main.auth_codes:
                auth_info = gui_main.auth_codes[sender_email]
                if isinstance(auth_info, dict):
                    return auth_info.get('auth_code', '')
                else:
                    return auth_info
            return None
        except:
            return None
    
    def _save_recall_history(self, task: RecallTask, success: bool, error_message: str = None):
        """保存撤回历史"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO recall_history 
                (recipient_email, sender_email, recall_time, recall_subject, 
                 recall_body, success, error_message)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                task.recipient_email,
                task.sender_email,
                datetime.datetime.now().isoformat(),
                task.recall_subject,
                task.recall_body,
                success,
                error_message
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"❌ 保存撤回历史失败: {str(e)}")
    
    def _update_task_status(self, task: RecallTask, success: bool):
        """更新任务状态"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            status = 'completed' if success else 'failed'
            
            cursor.execute('''
                UPDATE recall_tasks 
                SET status = ?, processed_time = ?, success = ?
                WHERE recipient_email = ? AND sender_email = ? AND trigger_time = ?
            ''', (
                status,
                datetime.datetime.now().isoformat(),
                success,
                task.recipient_email,
                task.sender_email,
                task.trigger_time
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"❌ 更新任务状态失败: {str(e)}")
    
    def _update_recipient_status(self, recipient_email: str, sender_email: str, status: str):
        """更新收件人状态"""
        try:
            # 更新质量数据库中的收件人状态
            conn = sqlite3.connect('recipient_quality.db', timeout=30.0)
            cursor = conn.cursor()
            
            # 确保表存在
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS recipient_status (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    recipient_email TEXT NOT NULL,
                    sender_email TEXT NOT NULL,
                    last_sent_time TEXT,
                    last_reply_time TEXT,
                    reply_count INTEGER DEFAULT 0,
                    bounce_count INTEGER DEFAULT 0,
                    status TEXT DEFAULT 'unknown',
                    notes TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(recipient_email, sender_email)
                )
            ''')
            
            # 更新或插入状态
            cursor.execute('''
                INSERT OR REPLACE INTO recipient_status 
                (recipient_email, sender_email, status, notes, updated_at)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                recipient_email,
                sender_email,
                status,
                f'自动撤回：检测到拒绝关键词',
                datetime.datetime.now().isoformat()
            ))
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"✅ 收件人状态已更新: {recipient_email} -> {status}")
            
        except Exception as e:
            self.logger.error(f"❌ 更新收件人状态失败: {str(e)}")
    
    def get_recall_statistics(self, sender_email: str = None) -> Dict:
        """获取撤回统计信息"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            where_clause = "WHERE sender_email = ?" if sender_email else ""
            params = [sender_email] if sender_email else []
            
            # 获取任务统计
            cursor.execute(f'''
                SELECT status, COUNT(*) FROM recall_tasks 
                {where_clause}
                GROUP BY status
            ''', params)
            
            task_stats = dict(cursor.fetchall())
            
            # 获取成功率
            cursor.execute(f'''
                SELECT COUNT(*) as total, SUM(success) as successful 
                FROM recall_history 
                {where_clause}
            ''', params)
            
            result = cursor.fetchone()
            total_recalls = result[0] if result else 0
            successful_recalls = result[1] if result else 0
            
            conn.close()
            
            success_rate = (successful_recalls / total_recalls * 100) if total_recalls > 0 else 0
            
            return {
                'task_statistics': task_stats,
                'total_recalls': total_recalls,
                'successful_recalls': successful_recalls,
                'success_rate': round(success_rate, 2),
                'pending_tasks': len(self.recall_queue)
            }
            
        except Exception as e:
            self.logger.error(f"❌ 获取撤回统计失败: {str(e)}")
            return {}

# 全局撤回管理器实例
_recall_manager = None

def get_recall_manager() -> IntelligentRecallManager:
    """获取全局撤回管理器实例"""
    global _recall_manager
    if _recall_manager is None:
        _recall_manager = IntelligentRecallManager()
    return _recall_manager

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能系统快速修复脚本
修复测试中发现的小问题，确保系统完美运行
"""

import logging
import sqlite3
import os
import json
import datetime
import sys

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_database_structure():
    """修复数据库结构问题"""
    logger.info("🔧 开始修复数据库结构...")
    
    databases = [
        'email_receiver.db',
        'recipient_quality.db',
        'qq_anti_spam.db',
        'anti_spam.db'
    ]
    
    for db_path in databases:
        if os.path.exists(db_path):
            try:
                conn = sqlite3.connect(db_path, timeout=30.0)
                cursor = conn.cursor()
                
                # 检查表是否存在
                cursor.execute('''
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name='recipient_status'
                ''')
                
                if cursor.fetchone():
                    # 检查列是否存在
                    cursor.execute("PRAGMA table_info(recipient_status)")
                    columns = [row[1] for row in cursor.fetchall()]
                    
                    # 添加缺失的列
                    if 'bounce_count' not in columns:
                        cursor.execute('ALTER TABLE recipient_status ADD COLUMN bounce_count INTEGER DEFAULT 0')
                        logger.info(f"✅ 添加bounce_count列到 {db_path}")
                    
                    if 'reply_count' not in columns:
                        cursor.execute('ALTER TABLE recipient_status ADD COLUMN reply_count INTEGER DEFAULT 0')
                        logger.info(f"✅ 添加reply_count列到 {db_path}")
                    
                    if 'quality_score' not in columns and 'quality' in db_path:
                        cursor.execute('ALTER TABLE recipient_status ADD COLUMN quality_score REAL DEFAULT 50.0')
                        logger.info(f"✅ 添加quality_score列到 {db_path}")
                
                else:
                    # 创建表
                    if 'quality' in db_path:
                        cursor.execute('''
                            CREATE TABLE recipient_status (
                                id INTEGER PRIMARY KEY AUTOINCREMENT,
                                recipient_email TEXT NOT NULL,
                                sender_email TEXT NOT NULL,
                                last_sent_time TEXT,
                                last_reply_time TEXT,
                                reply_count INTEGER DEFAULT 0,
                                bounce_count INTEGER DEFAULT 0,
                                status TEXT DEFAULT 'unknown',
                                quality_score REAL DEFAULT 50.0,
                                notes TEXT,
                                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                UNIQUE(recipient_email, sender_email)
                            )
                        ''')
                    else:
                        cursor.execute('''
                            CREATE TABLE recipient_status (
                                id INTEGER PRIMARY KEY AUTOINCREMENT,
                                recipient_email TEXT NOT NULL,
                                sender_email TEXT NOT NULL,
                                last_sent_time TEXT,
                                last_reply_time TEXT,
                                reply_count INTEGER DEFAULT 0,
                                bounce_count INTEGER DEFAULT 0,
                                status TEXT DEFAULT 'unknown',
                                notes TEXT,
                                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                UNIQUE(recipient_email, sender_email)
                            )
                        ''')
                    
                    logger.info(f"✅ 创建recipient_status表在 {db_path}")
                
                conn.commit()
                conn.close()
                
            except Exception as e:
                logger.error(f"❌ 修复数据库失败 {db_path}: {str(e)}")
        else:
            logger.warning(f"⚠️ 数据库不存在: {db_path}")

def fix_auto_reply_detection():
    """优化自动回复检测算法"""
    logger.info("🔧 优化自动回复检测算法...")
    
    try:
        # 读取email_receiver.py文件
        with open('email_receiver.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否需要优化普通回复的检测
        if '检测到短消息自动回复特征' in content:
            # 优化短消息检测逻辑
            old_logic = '''# 检查邮件长度（自动回复通常较短）
        if len(body) < 500 and any(word in body for word in ['thank', 'received', 'away', '收到', '谢谢', '感谢']):
            self.logger.info("检测到短消息自动回复特征")
            return True, 'auto_reply' '''
            
            new_logic = '''# 检查邮件长度（自动回复通常较短且包含特定模式）
        if len(body) < 200 and any(word in body.lower() for word in ['auto', 'automatic', 'away', 'office', '自动', '外出']):
            self.logger.info("检测到短消息自动回复特征")
            return True, 'auto_reply' '''
            
            if old_logic in content:
                content = content.replace(old_logic, new_logic)
                
                with open('email_receiver.py', 'w', encoding='utf-8') as f:
                    f.write(content)
                
                logger.info("✅ 优化了短消息自动回复检测逻辑")
            else:
                logger.info("ℹ️ 自动回复检测逻辑已是最新版本")
        
    except Exception as e:
        logger.error(f"❌ 优化自动回复检测失败: {str(e)}")

def create_auth_codes_template():
    """创建授权码配置模板"""
    logger.info("🔧 创建授权码配置模板...")
    
    try:
        if not os.path.exists('auth_codes.json'):
            template = {
                "<EMAIL>": "your_auth_code_here",
                "<EMAIL>": "your_auth_code_here",
                "<EMAIL>": "your_auth_code_here"
            }
            
            with open('auth_codes.json', 'w', encoding='utf-8') as f:
                json.dump(template, f, ensure_ascii=False, indent=2)
            
            logger.info("✅ 创建了授权码配置模板文件")
        else:
            logger.info("ℹ️ 授权码配置文件已存在")
    
    except Exception as e:
        logger.error(f"❌ 创建授权码配置失败: {str(e)}")

def optimize_recall_manager():
    """优化撤回管理器"""
    logger.info("🔧 优化撤回管理器...")
    
    try:
        # 检查智能撤回管理器文件
        if os.path.exists('intelligent_recall_manager.py'):
            with open('intelligent_recall_manager.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 优化授权码获取逻辑
            if 'def _get_auth_code' in content:
                # 添加更多授权码获取方式
                additional_code = '''
    def _get_auth_code_from_gui(self, sender_email: str) -> Optional[str]:
        """从GUI配置获取授权码"""
        try:
            # 尝试从GUI的auth_codes字典获取
            import gui_main
            if hasattr(gui_main, 'auth_codes') and sender_email in gui_main.auth_codes:
                auth_info = gui_main.auth_codes[sender_email]
                if isinstance(auth_info, dict):
                    return auth_info.get('auth_code', '')
                else:
                    return auth_info
            return None
        except:
            return None'''
            
            if '_get_auth_code_from_gui' not in content:
                # 在_get_auth_code方法后添加新方法
                insert_pos = content.find('def _save_recall_history')
                if insert_pos > 0:
                    content = content[:insert_pos] + additional_code + '\n    \n    ' + content[insert_pos:]
                    
                    with open('intelligent_recall_manager.py', 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    logger.info("✅ 优化了撤回管理器的授权码获取逻辑")
        
    except Exception as e:
        logger.error(f"❌ 优化撤回管理器失败: {str(e)}")

def create_system_status_checker():
    """创建系统状态检查器"""
    logger.info("🔧 创建系统状态检查器...")
    
    try:
        status_checker_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统状态检查器 - 检查智能系统的运行状态
"""

import os
import sqlite3
import json
import datetime

def check_system_status():
    """检查系统状态"""
    status = {
        'timestamp': datetime.datetime.now().isoformat(),
        'databases': {},
        'files': {},
        'overall_status': 'healthy'
    }
    
    # 检查数据库
    databases = [
        'email_receiver.db',
        'recipient_quality.db', 
        'intelligent_recall.db'
    ]
    
    for db in databases:
        if os.path.exists(db):
            try:
                conn = sqlite3.connect(db, timeout=10.0)
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
                table_count = cursor.fetchone()[0]
                conn.close()
                
                status['databases'][db] = {
                    'exists': True,
                    'table_count': table_count,
                    'status': 'ok'
                }
            except Exception as e:
                status['databases'][db] = {
                    'exists': True,
                    'error': str(e),
                    'status': 'error'
                }
                status['overall_status'] = 'warning'
        else:
            status['databases'][db] = {
                'exists': False,
                'status': 'missing'
            }
    
    # 检查关键文件
    files = [
        'email_receiver.py',
        'intelligent_recall_manager.py',
        'status_update_fixer.py',
        'auth_codes.json'
    ]
    
    for file in files:
        status['files'][file] = {
            'exists': os.path.exists(file),
            'size': os.path.getsize(file) if os.path.exists(file) else 0
        }
    
    return status

if __name__ == "__main__":
    status = check_system_status()
    print(json.dumps(status, ensure_ascii=False, indent=2))
'''
        
        with open('system_status_checker.py', 'w', encoding='utf-8') as f:
            f.write(status_checker_code)
        
        logger.info("✅ 创建了系统状态检查器")
        
    except Exception as e:
        logger.error(f"❌ 创建系统状态检查器失败: {str(e)}")

def create_quick_start_script():
    """创建快速启动脚本"""
    logger.info("🔧 创建快速启动脚本...")
    
    try:
        quick_start_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能系统快速启动脚本
"""

import os
import sys
import subprocess
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """主启动函数"""
    logger.info("🚀 启动智能邮件系统...")
    
    try:
        # 检查系统状态
        logger.info("📊 检查系统状态...")
        if os.path.exists('system_status_checker.py'):
            subprocess.run([sys.executable, 'system_status_checker.py'], check=True)
        
        # 启动主程序
        logger.info("🎯 启动主程序...")
        if os.path.exists('gui_main.py'):
            subprocess.run([sys.executable, 'gui_main.py'], check=True)
        else:
            logger.error("❌ 主程序文件不存在")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 启动失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
'''
        
        with open('quick_start_intelligent.py', 'w', encoding='utf-8') as f:
            f.write(quick_start_code)
        
        logger.info("✅ 创建了快速启动脚本")
        
    except Exception as e:
        logger.error(f"❌ 创建快速启动脚本失败: {str(e)}")

def main():
    """主修复函数"""
    logger.info("🚀 开始智能系统快速修复...")
    
    try:
        # 1. 修复数据库结构
        fix_database_structure()
        
        # 2. 优化自动回复检测
        fix_auto_reply_detection()
        
        # 3. 创建授权码配置模板
        create_auth_codes_template()
        
        # 4. 优化撤回管理器
        optimize_recall_manager()
        
        # 5. 创建系统状态检查器
        create_system_status_checker()
        
        # 6. 创建快速启动脚本
        create_quick_start_script()
        
        logger.info("✅ 智能系统快速修复完成！")
        logger.info("📋 修复摘要:")
        logger.info("   ✅ 数据库结构已修复")
        logger.info("   ✅ 自动回复检测已优化")
        logger.info("   ✅ 授权码配置模板已创建")
        logger.info("   ✅ 撤回管理器已优化")
        logger.info("   ✅ 系统状态检查器已创建")
        logger.info("   ✅ 快速启动脚本已创建")
        
        logger.info("🎯 使用建议:")
        logger.info("   1. 编辑 auth_codes.json 配置您的邮箱授权码")
        logger.info("   2. 运行 python system_status_checker.py 检查系统状态")
        logger.info("   3. 运行 python quick_start_intelligent.py 快速启动系统")
        logger.info("   4. 使用 python test_intelligent_improvements.py 测试功能")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 快速修复失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

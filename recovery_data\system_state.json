{"timestamp": "2025-06-16T20:23:24.258558", "python_path": "C:\\Program Files\\Python311\\python.exe", "working_directory": "E:\\自动发邮件", "installed_packages": ["autocommand==2.2.2", "backports.tarfile==1.2.0", "certifi==2025.4.26", "charset-normalizer==3.4.2", "customtkinter==5.2.2", "darkdetect==0.8.0", "idna==3.10", "importlib_metadata==8.7.0", "jaraco.context==6.0.1", "jaraco.functools==4.1.0", "jaraco.text==4.0.0", "jieba==0.42.1", "more-itertools==10.7.0", "packaging==25.0", "pillow==11.2.1", "pip==25.1.1", "psutil==7.0.0", "requests==2.32.4", "setuptools==65.5.0", "urllib3==2.4.0", "zipp==3.23.0"], "config_files": {"all_features_config.json": {"exists": true, "size": 304, "modified": 1749884304.6859314}, "automation_workflow.json": {"exists": true, "size": 620, "modified": 1749831040.5675495}, "auth_codes.json": {"exists": true, "size": 112, "modified": 1750061052.105558}, "monitor_settings.json": {"exists": true, "size": 126, "modified": 1750037486.3411312}, "startup_config.json": {"exists": true, "size": 266, "modified": 1749817298.5604556}}, "database_files": {"email_history.db": {"exists": true, "size": 11177984, "modified": 1750076519.6033907}, "recipient_quality.db": {"exists": true, "size": 634880, "modified": 1750076515.4539597}, "anti_spam.db": {"exists": true, "size": 57344, "modified": 1750076515.4881968}, "qq_anti_spam.db": {"exists": true, "size": 94208, "modified": 1750076515.5873125}, "user_data/user_settings.db": {"exists": true, "size": 53248, "modified": 1750076590.5558057}}, "user_settings": {"check_interval": "2", "monitor_duration": "6", "auto_start": "True", "save_time": "2025-06-16T09:31:26.363093", "body": "请在此输入邮件正文内容...\n\n支持中文、Emoji表情 😊", "send_mode": "safe", "add_personalization": "False", "auto_reply_monitoring": "True", "auto_queue_mode": "True", "sender_email": "<EMAIL>", "recipients": "<EMAIL>", "subject": "", "attachments": "[]", "email_queue": "[{\"id\": 1, \"sender_email\": \"<EMAIL>\", \"recipient_emails\": \"<EMAIL>\", \"subject\": \"\\u3010\\u77ed\\u7bc7\\u3011\\u3010\\u6211\\u6551\\u7684\\u4e5e\\u4e10\\u6210\\u4e86\\u9a78\\u9a6c\\u3011\\u3010\\u7cfb\\u7edf\\u3001\\u590d\\u4ec7\\u3011\\u30109120\\u5b57\\u3011\", \"body\": \"\\u8bf7\\u5728\\u6b64\\u8f93\\u5165\\u90ae\\u4ef6\\u6b63\\u6587\\u5185\\u5bb9...\\n\\n\\u652f\\u6301\\u4e2d\\u6587\\u3001Emoji\\u8868\\u60c5 \\ud83d\\ude0a\", \"attachments\": [\"D:/\\u4e1c\\u6d77/6\\u6708\\u77ed\\u7bc7/\\u8a00\\u60c5/6.16/\\u6211\\u6551\\u7684\\u4e5e\\u4e10\\u6210\\u4e86\\u9a78\\u9a6c.docx\"], \"send_mode\": \"safe\", \"add_personalization\": false, \"created_time\": \"2025-06-16T20:19:20.165089\", \"status\": \"completed\"}]", "auth_codes": "{\"<EMAIL>\": {\"auth_code\": \"rhwxeprlqeysdcda\", \"add_time\": \"2025-06-16 16:04:12\"}}", "smart_settings": "{\"monitoring_interval\": \"2\", \"monitoring_duration\": \"6\"}"}, "system_info": {"platform": "Windows-10-10.0.22631-SP0", "python_version": "3.11.0", "architecture": "64bit"}}
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件接收器 - 用于读取自动回复和分析收件人状态
"""

import imaplib
import email
import logging
import sqlite3
import datetime
import re
from typing import List, Dict, Optional, Tuple
from email.header import decode_header
from email.utils import parsedate_to_datetime
import time
import random

class EmailReceiver:
    """邮件接收器 - 专门用于处理自动回复"""
    
    def __init__(self, email_address: str, password: str, imap_server: str = "imap.qq.com", imap_port: int = 993):
        self.email_address = email_address
        self.password = password
        self.imap_server = imap_server
        self.imap_port = imap_port
        self.logger = logging.getLogger(__name__)
        self.db_path = "email_history.db"

        # 使用优化的数据库操作（解决锁定问题）

        # 初始化数据库
        self._init_reply_database()
        
        self.logger.info(f"邮件接收器初始化完成 - 邮箱: {email_address}")
    
    def _init_reply_database(self):
        """初始化自动回复数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建自动回复记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS auto_replies (
                id TEXT PRIMARY KEY,
                original_email_id TEXT,
                sender_email TEXT NOT NULL,
                recipient_email TEXT NOT NULL,
                reply_subject TEXT,
                reply_body TEXT,
                reply_time TEXT NOT NULL,
                reply_type TEXT,
                is_auto_reply INTEGER DEFAULT 1,
                delivery_status TEXT,
                bounce_reason TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建收件人状态表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS recipient_status (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                recipient_email TEXT NOT NULL,
                sender_email TEXT NOT NULL,
                last_sent_time TEXT,
                last_reply_time TEXT,
                reply_count INTEGER DEFAULT 0,
                bounce_count INTEGER DEFAULT 0,
                status TEXT DEFAULT 'unknown',
                notes TEXT,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(recipient_email, sender_email)
            )
        ''')
        
        # 创建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_auto_replies_recipient ON auto_replies(recipient_email)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_auto_replies_time ON auto_replies(reply_time)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_recipient_status_email ON recipient_status(recipient_email)')
        
        conn.commit()
        conn.close()
        
        self.logger.info("自动回复数据库初始化完成")
    
    def connect_imap(self) -> imaplib.IMAP4_SSL:
        """连接IMAP服务器"""
        try:
            self.logger.info(f"正在连接IMAP服务器: {self.imap_server}:{self.imap_port}")
            mail = imaplib.IMAP4_SSL(self.imap_server, self.imap_port)
            mail.login(self.email_address, self.password)
            self.logger.info("IMAP服务器连接成功")
            return mail
        except Exception as e:
            self.logger.error(f"IMAP连接失败: {str(e)}")
            raise
    
    def decode_mime_words(self, s: str) -> str:
        """解码MIME编码的字符串"""
        try:
            decoded_parts = decode_header(s)
            decoded_string = ""
            for part, encoding in decoded_parts:
                if isinstance(part, bytes):
                    if encoding:
                        decoded_string += part.decode(encoding)
                    else:
                        decoded_string += part.decode('utf-8', errors='ignore')
                else:
                    decoded_string += part
            return decoded_string
        except Exception as e:
            self.logger.warning(f"解码MIME字符串失败: {str(e)}")
            return str(s)
    
    def extract_email_content(self, msg) -> Dict:
        """提取邮件内容（改进版）"""
        try:
            # 提取基本信息
            subject = self.decode_mime_words(msg.get('Subject', ''))
            from_addr = self.decode_mime_words(msg.get('From', ''))
            to_addr = self.decode_mime_words(msg.get('To', ''))
            date_str = msg.get('Date', '')

            # 解析日期
            try:
                date_obj = parsedate_to_datetime(date_str)
                reply_time = date_obj.isoformat()
            except:
                reply_time = datetime.datetime.now().isoformat()

            # 提取邮件正文
            body = ""
            html_body = ""

            if msg.is_multipart():
                for part in msg.walk():
                    content_type = part.get_content_type()
                    if content_type == "text/plain":
                        payload = part.get_payload(decode=True)
                        if payload:
                            try:
                                body += payload.decode('utf-8', errors='ignore')
                            except:
                                try:
                                    body += payload.decode('gbk', errors='ignore')
                                except:
                                    body += str(payload)
                    elif content_type == "text/html":
                        payload = part.get_payload(decode=True)
                        if payload:
                            try:
                                html_body += payload.decode('utf-8', errors='ignore')
                            except:
                                try:
                                    html_body += payload.decode('gbk', errors='ignore')
                                except:
                                    html_body += str(payload)
            else:
                payload = msg.get_payload(decode=True)
                if payload:
                    try:
                        body = payload.decode('utf-8', errors='ignore')
                    except:
                        try:
                            body = payload.decode('gbk', errors='ignore')
                        except:
                            body = str(payload)

            # 如果没有纯文本内容，尝试从HTML中提取
            if not body and html_body:
                try:
                    import re
                    # 简单的HTML标签清理
                    body = re.sub(r'<[^>]+>', '', html_body)
                    body = re.sub(r'\s+', ' ', body).strip()
                except:
                    body = html_body

            # 提取更多头部信息
            headers = {}
            for key, value in msg.items():
                headers[key.lower()] = value

            return {
                'subject': subject,
                'from': from_addr,
                'to': to_addr,
                'body': body,
                'html_body': html_body,
                'reply_time': reply_time,
                'message_id': msg.get('Message-ID', ''),
                'in_reply_to': msg.get('In-Reply-To', ''),
                'references': msg.get('References', ''),
                'headers': headers,
                'auto_submitted': headers.get('auto-submitted', ''),
                'x_auto_response_suppress': headers.get('x-auto-response-suppress', ''),
                'precedence': headers.get('precedence', '')
            }

        except Exception as e:
            self.logger.error(f"提取邮件内容失败: {str(e)}")
            return {}
    
    def is_auto_reply(self, email_content: Dict) -> Tuple[bool, str]:
        """判断是否为自动回复（深度语义分析版）"""
        subject = email_content.get('subject', '').lower()
        body = email_content.get('body', '').lower()
        from_addr = email_content.get('from', '').lower()

        # 首先进行深度语义分析
        semantic_result = self._deep_semantic_analysis(subject, body)
        if semantic_result:
            return semantic_result

        # 退信关键词（优先检查）
        bounce_keywords = [
            'delivery failure', 'mail delivery failed', 'undelivered',
            'user unknown', 'mailbox full', 'quota exceeded',
            'permanent failure', 'temporary failure', 'bounce',
            '用户不存在', '邮箱已满', '投递失败', '退信',
            'mailer-daemon', 'postmaster', 'mail delivery subsystem'
        ]

        # 强拒绝关键词（明确拒绝，需要自动撤回）
        strong_rejection_keywords = [
            '不再接收', '请勿发送', '拒绝接收', '停止投稿',
            '不接受稿件', '请停止', '别再发了', '不需要了',
            'stop sending', 'no more emails', 'unsubscribe', 'remove me',
            'do not send', 'stop emails', 'cease sending', 'discontinue',
            '取消订阅', '退订', '移除邮箱', '停止邮件',
            '不要再发', '请勿再发', '停止发送'
        ]

        # 临时暂停关键词（需要上下文判断）
        temporary_pause_keywords = [
            '暂停收稿', '停止收稿', '暂停投稿'
        ]

        # 积极合作信号（表示不应撤回）
        positive_signals = [
            '感谢投稿', '已经收到', '正在审核', '审核期', '过稿',
            '收稿方向', '主收', '联系方式', 'qq', '微信',
            'thank you', 'received', 'reviewing', 'contact',
            '合作', '欢迎', '期待', '继续'
        ]

        # 自动回复关键词
        auto_reply_keywords = [
            'auto reply', 'automatic reply', 'out of office', 'vacation',
            'away message', 'auto response', 'automated response',
            '自动回复', '自动回覆', '休假', '外出', '不在办公室',
            'thank you for your email', 'thanks for your message',
            'i am currently', 'i will be', 'currently away',
            '谢谢您的邮件', '感谢您的来信', '我目前', '我现在'
        ]

        # QQ邮箱特殊自动回复标识
        qq_auto_reply_keywords = [
            'qq邮箱自动回复', 'qq mail auto reply',
            'qq邮箱自动回覆', 'qqmail auto reply',
            '腾讯邮箱自动回复', '腾讯自动回复',
            'tencent auto reply', 'tencent automatic reply'
        ]

        # 检查主题和正文（转换为小写进行匹配）
        text_to_check = f"{subject} {body} {from_addr}".lower()

        # 优先检查退信
        for keyword in bounce_keywords:
            if keyword.lower() in text_to_check:
                self.logger.info(f"检测到退信关键词: {keyword}")
                return True, 'bounce'

        # 智能拒绝检测（优化版）
        # 1. 首先检查强拒绝关键词
        for keyword in strong_rejection_keywords:
            if keyword.lower() in text_to_check:
                self.logger.warning(f"🚨 检测到强拒绝关键词: {keyword}")
                return True, 'rejection'

        # 2. 检查临时暂停关键词，但需要上下文判断
        temp_pause_detected = False
        for keyword in temporary_pause_keywords:
            if keyword.lower() in text_to_check:
                temp_pause_detected = True
                break

        if temp_pause_detected:
            # 检查是否有积极合作信号
            has_positive_signals = any(signal.lower() in text_to_check for signal in positive_signals)

            # 检查是否有时间限制词（表示临时暂停）
            time_indicators = ['天', '日', '周', '月', '小时', '分钟', '明天', '下周', '下月',
                             'day', 'week', 'month', 'hour', 'minute', 'tomorrow', 'next']
            has_time_limit = any(indicator in text_to_check for indicator in time_indicators)

            # 检查是否有恢复说明
            resume_indicators = ['恢复', '继续', '重新', '周一', '上班', '审稿', 'resume', 'continue', 'monday']
            has_resume_plan = any(indicator in text_to_check for indicator in resume_indicators)

            if has_positive_signals or has_time_limit or has_resume_plan:
                self.logger.info(f"🔍 检测到临时暂停但有积极信号，判断为正常业务通知: {keyword if 'keyword' in locals() else '暂停相关'}")
                return True, 'business_notice'  # 新类型：业务通知
            else:
                self.logger.warning(f"🚨 检测到永久性暂停: {keyword if 'keyword' in locals() else '暂停相关'}")
                return True, 'rejection'

        # 检查QQ邮箱特殊标识
        for keyword in qq_auto_reply_keywords:
            if keyword.lower() in text_to_check:
                self.logger.info(f"检测到QQ自动回复关键词: {keyword}")
                return True, 'auto_reply'

        # 检查一般自动回复
        for keyword in auto_reply_keywords:
            if keyword.lower() in text_to_check:
                self.logger.info(f"检测到自动回复关键词: {keyword}")
                return True, 'auto_reply'

        # 额外检查：如果是QQ邮箱且包含特定模式
        if 'qq.com' in from_addr.lower():
            # QQ邮箱的特殊自动回复模式
            qq_patterns = [
                '您好，我现在有事不在',
                '您好！我现在不在',
                '谢谢您的邮件，我目前',
                '感谢您的来信，我现在',
                '自动回复：',
                'auto reply:',
                'automatic reply:'
            ]

            for pattern in qq_patterns:
                if pattern.lower() in text_to_check:
                    self.logger.info(f"检测到QQ邮箱自动回复模式: {pattern}")
                    return True, 'auto_reply'

        # 检查特殊头部
        headers = email_content.get('headers', {})
        if 'auto-submitted' in headers:
            self.logger.info("检测到Auto-Submitted头部")
            return True, 'auto_reply'

        # 检查邮件长度（自动回复通常较短）
        if len(body) < 500 and any(word in body for word in ['thank', 'received', 'away', '收到', '谢谢', '感谢']):
            self.logger.info("检测到短消息自动回复特征")
            return True, 'auto_reply'

        # 检查是否包含常见的自动回复模式
        auto_reply_patterns = [
            r'thank.*you.*for.*your.*email',
            r'i.*am.*currently.*away',
            r'out.*of.*office',
            r'will.*respond.*when.*i.*return',
            r'谢谢.*您.*的.*邮件',
            r'我.*目前.*不在',
            r'外出.*办公'
        ]

        import re
        for pattern in auto_reply_patterns:
            if re.search(pattern, text_to_check, re.IGNORECASE):
                self.logger.info(f"检测到自动回复模式: {pattern}")
                return True, 'auto_reply'

        return False, 'normal'

    def _deep_semantic_analysis(self, subject: str, body: str) -> Optional[Tuple[bool, str]]:
        """深度语义分析 - 通读全文进行智能判断"""
        try:
            full_text = f"{subject} {body}".lower()

            # 撤回要求检测（最高优先级）
            withdrawal_patterns = [
                # 直接要求撤回
                '撤回', '撤稿', '撤销', '取消投稿',
                'withdraw', 'retract', 'cancel submission',

                # 要求自行撤回
                '自行撤稿', '自己撤回', '动动手指.*撤',
                '麻烦.*撤', '请.*撤回', '请.*撤稿',

                # 不会处理的表述
                '不会打开', '不会查看', '不会处理',
                '不打开文件', '不看稿子', '不审稿',
                'will not open', 'will not review', 'will not process'
            ]

            # 检查是否包含撤回要求
            import re
            for pattern in withdrawal_patterns:
                if re.search(pattern, full_text):
                    self.logger.warning(f"🚨 检测到撤回要求: {pattern}")
                    return True, 'rejection'

            # 建议去别处的表述（表示拒绝）
            redirect_patterns = [
                '在别处.*大麦', '去别处', '别的地方',
                '其他平台', '其他地方', '换个地方',
                'try elsewhere', 'go somewhere else', 'other platforms'
            ]

            for pattern in redirect_patterns:
                if re.search(pattern, full_text):
                    self.logger.warning(f"🚨 检测到转向建议: {pattern}")
                    return True, 'rejection'

            # 当前不处理但表达方式温和的情况
            gentle_rejection_patterns = [
                # 温和的拒绝表述
                '暂时不.*收稿.*别慌', '不会打开.*撤回就好',
                '暂停.*自行撤稿', '不对外收稿.*撤回',

                # 组合判断：暂停 + 撤回要求
                ('暂停', '撤'),
                ('不收', '撤'),
                ('停止', '撤')
            ]

            for pattern in gentle_rejection_patterns:
                if isinstance(pattern, tuple):
                    # 组合模式：两个词都要出现
                    if all(word in full_text for word in pattern):
                        self.logger.warning(f"🚨 检测到温和拒绝组合: {pattern}")
                        return True, 'rejection'
                else:
                    # 单一模式
                    if re.search(pattern, full_text):
                        self.logger.warning(f"🚨 检测到温和拒绝: {pattern}")
                        return True, 'rejection'

            # 未来合作但当前拒绝的情况
            future_cooperation_patterns = [
                # 表达未来合作意愿但当前拒绝
                '期待.*合作.*撤稿', '以后.*合作.*暂停',
                '未来.*收稿.*撤回', '后续.*恢复.*撤稿'
            ]

            for pattern in future_cooperation_patterns:
                if re.search(pattern, full_text):
                    self.logger.warning(f"🚨 检测到未来合作但当前拒绝: {pattern}")
                    return True, 'rejection'

            # 语义权重分析
            rejection_score = 0
            cooperation_score = 0

            # 拒绝信号权重计算
            rejection_signals = [
                ('撤回', 10), ('撤稿', 10), ('不会打开', 8),
                ('自行撤稿', 9), ('暂停收稿', 5), ('不对外收稿', 6),
                ('在别处', 7), ('别的地方', 6)
            ]

            for signal, weight in rejection_signals:
                if signal in full_text:
                    rejection_score += weight

            # 合作信号权重计算
            cooperation_signals = [
                ('感谢投稿', 8), ('已收到', 6), ('正在审核', 8),
                ('收稿方向', 7), ('联系方式', 5), ('qq', 3),
                ('期待合作', 4), ('欢迎投稿', 6)
            ]

            for signal, weight in cooperation_signals:
                if signal in full_text:
                    cooperation_score += weight

            # 综合判断
            if rejection_score >= 8:  # 拒绝信号强烈
                if cooperation_score < rejection_score * 0.5:  # 合作信号不足以抵消
                    self.logger.warning(f"🚨 语义分析判定为拒绝 (拒绝分:{rejection_score}, 合作分:{cooperation_score})")
                    return True, 'rejection'

            # 如果没有明确的拒绝信号，返回None继续后续检查
            return None

        except Exception as e:
            self.logger.error(f"❌ 深度语义分析失败: {str(e)}")
            return None

    def _trigger_auto_recall(self, recipient_email: str, email_content: Dict):
        """触发自动撤回机制（智能增强版）"""
        try:
            self.logger.info(f"🔄 开始智能自动撤回流程: {recipient_email}")

            # 使用智能撤回管理器
            try:
                from intelligent_recall_manager import get_recall_manager
                recall_manager = get_recall_manager()

                # 构建触发原因
                trigger_reason = f"检测到拒绝关键词: {email_content.get('subject', '')}"

                # 自定义撤回模板
                custom_template = {
                    'subject': '重要通知：根据您的回复，已停止发送',
                    'body': f'''尊敬的收件人：

根据您的回复内容，我们理解您不希望继续接收此类邮件。

我们已将您的邮箱从发送列表中移除，不会再向您发送类似邮件。

如有任何疑问或误解，请联系我们。

谢谢您的理解！

---
此邮件为系统根据您的回复自动发送
原回复内容：{email_content.get('body', '')[:100]}...'''
                }

                # 添加撤回任务
                success = recall_manager.add_recall_task(
                    recipient_email,
                    self.email_address,
                    trigger_reason,
                    custom_template
                )

                if success:
                    self.logger.info(f"✅ 智能撤回任务已创建: {recipient_email}")
                else:
                    self.logger.error(f"❌ 创建智能撤回任务失败: {recipient_email}")
                    # 回退到原有方法
                    self._fallback_recall_trigger(recipient_email, email_content)

            except ImportError:
                self.logger.warning("⚠️ 智能撤回管理器不可用，使用传统方法")
                self._fallback_recall_trigger(recipient_email, email_content)
            except Exception as e:
                self.logger.error(f"❌ 智能撤回失败，使用传统方法: {str(e)}")
                self._fallback_recall_trigger(recipient_email, email_content)

        except Exception as e:
            self.logger.error(f"❌ 自动撤回触发失败: {str(e)}")

    def _fallback_recall_trigger(self, recipient_email: str, email_content: Dict):
        """传统撤回触发方法（回退方案）"""
        try:
            # 记录撤回触发事件
            recall_info = {
                'recipient_email': recipient_email,
                'sender_email': self.email_address,
                'trigger_time': datetime.datetime.now().isoformat(),
                'trigger_reason': f"检测到拒绝关键词: {email_content.get('subject', '')}",
                'original_body': email_content.get('body', '')[:200],
                'status': 'triggered'
            }

            # 保存撤回记录
            self._save_recall_trigger(recall_info)

            # 通知系统进行撤回（通过回调或事件机制）
            if hasattr(self, 'recall_callback') and self.recall_callback:
                self.recall_callback(recipient_email, recall_info)
            else:
                # 如果没有回调，记录到文件供GUI读取
                self._save_recall_notification(recall_info)

            self.logger.info(f"✅ 传统撤回触发完成: {recipient_email}")

        except Exception as e:
            self.logger.error(f"❌ 传统撤回触发失败: {str(e)}")

    def _save_recall_trigger(self, recall_info: Dict):
        """保存撤回触发记录"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()

            # 创建撤回触发表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS recall_triggers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    recipient_email TEXT NOT NULL,
                    sender_email TEXT NOT NULL,
                    trigger_time TEXT NOT NULL,
                    trigger_reason TEXT,
                    original_body TEXT,
                    status TEXT DEFAULT 'triggered',
                    processed_time TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            cursor.execute('''
                INSERT INTO recall_triggers
                (recipient_email, sender_email, trigger_time, trigger_reason, original_body, status)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                recall_info['recipient_email'],
                recall_info['sender_email'],
                recall_info['trigger_time'],
                recall_info['trigger_reason'],
                recall_info['original_body'],
                recall_info['status']
            ))

            conn.commit()
            conn.close()

            self.logger.info(f"✅ 撤回触发记录已保存: {recall_info['recipient_email']}")

        except Exception as e:
            self.logger.error(f"❌ 保存撤回触发记录失败: {str(e)}")

    def _save_recall_notification(self, recall_info: Dict):
        """保存撤回通知到文件"""
        try:
            import json
            import os

            notification_file = 'auto_recall_notifications.json'

            # 读取现有通知
            notifications = []
            if os.path.exists(notification_file):
                try:
                    with open(notification_file, 'r', encoding='utf-8') as f:
                        notifications = json.load(f)
                except:
                    notifications = []

            # 添加新通知
            notifications.append(recall_info)

            # 保存通知
            with open(notification_file, 'w', encoding='utf-8') as f:
                json.dump(notifications, f, ensure_ascii=False, indent=2)

            self.logger.info(f"✅ 撤回通知已保存到文件: {notification_file}")

        except Exception as e:
            self.logger.error(f"❌ 保存撤回通知文件失败: {str(e)}")

    def set_recall_callback(self, callback):
        """设置撤回回调函数"""
        self.recall_callback = callback
        self.logger.info("✅ 撤回回调函数已设置")
    
    def extract_original_recipient(self, email_content: Dict) -> Optional[str]:
        """从自动回复中提取原始收件人邮箱"""
        from_addr = email_content.get('from', '')
        
        # 使用正则表达式提取邮箱地址
        email_pattern = r'[\w\.-]+@[\w\.-]+\.\w+'
        matches = re.findall(email_pattern, from_addr)
        
        if matches:
            return matches[0]
        
        return None
    
    def check_recent_replies(self, hours: int = 24, target_recipients: List[str] = None) -> List[Dict]:
        """检查最近的自动回复

        Args:
            hours: 检查最近多少小时的邮件
            target_recipients: 目标收件人列表，如果提供则只监控这些收件人的回复
        """
        try:
            mail = self.connect_imap()
            mail.select('INBOX')
            
            # 计算时间范围
            since_date = (datetime.datetime.now() - datetime.timedelta(hours=hours)).strftime('%d-%b-%Y')
            
            # 搜索最近的邮件
            status, messages = mail.search(None, f'SINCE {since_date}')
            
            if status != 'OK':
                self.logger.warning("搜索邮件失败")
                return []
            
            message_ids = messages[0].split()
            replies = []
            
            self.logger.info(f"找到 {len(message_ids)} 封最近 {hours} 小时的邮件")
            
            for msg_id in message_ids[-50:]:  # 只处理最近50封邮件
                try:
                    status, msg_data = mail.fetch(msg_id, '(RFC822)')
                    if status == 'OK':
                        email_body = msg_data[0][1]
                        msg = email.message_from_bytes(email_body)
                        
                        email_content = self.extract_email_content(msg)
                        if email_content:
                            is_auto, reply_type = self.is_auto_reply(email_content)
                            
                            if is_auto:
                                recipient = self.extract_original_recipient(email_content)
                                if recipient:
                                    # 如果指定了目标收件人列表，只监控这些收件人
                                    if target_recipients is not None and recipient not in target_recipients:
                                        self.logger.debug(f"跳过非目标收件人的回复: {recipient}")
                                        continue

                                    reply_info = {
                                        'recipient_email': recipient,
                                        'reply_type': reply_type,
                                        'reply_time': email_content['reply_time'],
                                        'subject': email_content['subject'],
                                        'body': email_content['body'][:500],  # 限制长度
                                        'sender_email': self.email_address
                                    }
                                    replies.append(reply_info)

                                    # 保存到数据库
                                    self.save_auto_reply(reply_info)

                                    # 智能处理：根据回复类型采取不同行动
                                    if reply_type == 'rejection':
                                        self.logger.warning(f"🚨 检测到明确拒绝，触发自动撤回: {recipient}")
                                        self._trigger_auto_recall(recipient, email_content)

                                        # 更新收件人状态为拒绝
                                        try:
                                            from status_update_fixer import get_status_fixer
                                            status_fixer = get_status_fixer()
                                            status_fixer.update_recipient_status_safe(
                                                recipient, self.email_address, 'rejected',
                                                'auto_reply', '检测到明确拒绝关键词'
                                            )
                                        except Exception as e:
                                            self.logger.error(f"❌ 更新拒绝状态失败: {str(e)}")

                                    elif reply_type == 'business_notice':
                                        self.logger.info(f"📋 检测到业务通知，不触发撤回: {recipient}")
                                        # 更新为活跃状态，因为对方仍在合作
                                        try:
                                            from status_update_fixer import get_status_fixer
                                            status_fixer = get_status_fixer()
                                            status_fixer.update_recipient_status_safe(
                                                recipient, self.email_address, 'active',
                                                'auto_reply', '业务通知，仍在合作'
                                            )
                                        except Exception as e:
                                            self.logger.error(f"❌ 更新业务状态失败: {str(e)}")

                                    self.logger.info(f"✅ 发现目标收件人回复: {recipient} -> {reply_type}")
                
                except Exception as e:
                    self.logger.warning(f"处理邮件 {msg_id} 失败: {str(e)}")
                    continue
            
            mail.close()
            mail.logout()
            
            self.logger.info(f"检查完成，发现 {len(replies)} 个自动回复")
            return replies
            
        except Exception as e:
            self.logger.error(f"检查自动回复失败: {str(e)}")
            return []
    
    def save_auto_reply(self, reply_info: Dict):
        """保存自动回复到数据库（优化版）"""
        try:
            # 添加随机延迟避免并发冲突
            time.sleep(random.uniform(0.01, 0.03))

            max_retries = 5
            for attempt in range(max_retries):
                try:
                    conn = sqlite3.connect(
                        self.db_path,
                        timeout=30.0,  # 增加超时时间
                        check_same_thread=False
                    )

                    # 优化设置
                    conn.execute("PRAGMA journal_mode=WAL")
                    conn.execute("PRAGMA synchronous=NORMAL")
                    conn.execute("PRAGMA busy_timeout=30000")

                    cursor = conn.cursor()

                    # 生成唯一ID
                    reply_id = f"reply_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}_{hash(reply_info['recipient_email']) % 10000:04d}"

                    cursor.execute('''
                        INSERT OR REPLACE INTO auto_replies
                        (id, sender_email, recipient_email, reply_subject, reply_body,
                         reply_time, reply_type, delivery_status)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        reply_id,
                        reply_info['sender_email'],
                        reply_info['recipient_email'],
                        reply_info['subject'],
                        reply_info['body'],
                        reply_info['reply_time'],
                        reply_info['reply_type'],
                        'received' if reply_info['reply_type'] == 'auto_reply' else 'bounced'
                    ))

                    conn.commit()
                    conn.close()

                    # 成功则退出重试循环
                    break

                except sqlite3.OperationalError as e:
                    if conn:
                        try:
                            conn.close()
                        except:
                            pass

                    if "database is locked" in str(e) and attempt < max_retries - 1:
                        # 指数退避 + 随机抖动
                        delay = (0.1 * (2 ** attempt)) + random.uniform(0, 0.1)
                        time.sleep(delay)
                        self.logger.warning(f"数据库锁定，第 {attempt + 1} 次重试（延迟 {delay:.2f}s）")
                        continue
                    else:
                        raise

            # 更新收件人状态
            self.update_recipient_status(reply_info['recipient_email'], reply_info['sender_email'], reply_info['reply_type'])

            self.logger.debug(f"保存自动回复: {reply_info['recipient_email']} - {reply_info['reply_type']}")

        except Exception as e:
            self.logger.error(f"保存自动回复失败: {str(e)}")
    
    def update_recipient_status(self, recipient_email: str, sender_email: str, reply_type: str):
        """更新收件人状态（优化版）"""
        # 添加随机延迟避免并发冲突
        time.sleep(random.uniform(0.01, 0.03))

        max_retries = 5
        for attempt in range(max_retries):
            try:
                conn = sqlite3.connect(
                    self.db_path,
                    timeout=30.0,  # 增加超时时间
                    check_same_thread=False
                )

                # 优化设置
                conn.execute("PRAGMA journal_mode=WAL")
                conn.execute("PRAGMA synchronous=NORMAL")
                conn.execute("PRAGMA busy_timeout=30000")

                cursor = conn.cursor()

                # 获取当前状态
                cursor.execute('''
                    SELECT reply_count, bounce_count, status FROM recipient_status
                    WHERE recipient_email = ? AND sender_email = ?
                ''', (recipient_email, sender_email))

                result = cursor.fetchone()

                if result:
                    reply_count, bounce_count, current_status = result

                    if reply_type == 'auto_reply':
                        reply_count += 1
                        new_status = 'active'
                    elif reply_type == 'bounce':
                        bounce_count += 1
                        new_status = 'invalid' if bounce_count >= 2 else 'problematic'
                    else:
                        new_status = current_status

                    cursor.execute('''
                        UPDATE recipient_status
                        SET reply_count = ?, bounce_count = ?, status = ?,
                            last_reply_time = ?, updated_at = CURRENT_TIMESTAMP
                        WHERE recipient_email = ? AND sender_email = ?
                    ''', (reply_count, bounce_count, new_status,
                          datetime.datetime.now().isoformat(), recipient_email, sender_email))
                else:
                    # 新建记录
                    reply_count = 1 if reply_type == 'auto_reply' else 0
                    bounce_count = 1 if reply_type == 'bounce' else 0
                    status = 'active' if reply_type == 'auto_reply' else ('invalid' if reply_type == 'bounce' else 'unknown')

                    cursor.execute('''
                        INSERT INTO recipient_status
                        (recipient_email, sender_email, last_reply_time, reply_count, bounce_count, status)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (recipient_email, sender_email, datetime.datetime.now().isoformat(),
                          reply_count, bounce_count, status))

                conn.commit()
                conn.close()

                # 成功则退出重试循环
                self.logger.info(f"✅ 收件人状态更新成功: {recipient_email} -> {reply_type}")
                return

            except sqlite3.OperationalError as e:
                if conn:
                    try:
                        conn.close()
                    except:
                        pass

                if "database is locked" in str(e) and attempt < max_retries - 1:
                    # 指数退避 + 随机抖动
                    delay = (0.1 * (2 ** attempt)) + random.uniform(0, 0.1)
                    time.sleep(delay)
                    self.logger.warning(f"数据库锁定，第 {attempt + 1} 次重试（延迟 {delay:.2f}s）")
                    continue
                else:
                    self.logger.error(f"更新收件人状态失败: {str(e)}")
                    raise
            except Exception as e:
                if conn:
                    try:
                        conn.close()
                    except:
                        pass
                self.logger.error(f"更新收件人状态失败: {str(e)}")
                raise
    
    def get_recipient_analysis(self, sender_email: str) -> Dict:
        """获取收件人分析报告"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 统计各种状态的收件人
            cursor.execute('''
                SELECT status, COUNT(*) as count
                FROM recipient_status
                WHERE sender_email = ?
                GROUP BY status
            ''', (sender_email,))
            
            status_counts = dict(cursor.fetchall())
            
            # 获取最近的自动回复
            cursor.execute('''
                SELECT recipient_email, reply_type, reply_time, reply_subject
                FROM auto_replies
                WHERE sender_email = ?
                ORDER BY reply_time DESC
                LIMIT 20
            ''', (sender_email,))
            
            recent_replies = cursor.fetchall()
            
            # 获取问题收件人
            cursor.execute('''
                SELECT recipient_email, status, bounce_count, last_reply_time, notes
                FROM recipient_status
                WHERE sender_email = ? AND (status = 'invalid' OR bounce_count > 0)
                ORDER BY bounce_count DESC, updated_at DESC
            ''', (sender_email,))
            
            problematic_recipients = cursor.fetchall()
            
            conn.close()
            
            return {
                'status_summary': status_counts,
                'recent_replies': recent_replies,
                'problematic_recipients': problematic_recipients,
                'total_recipients': sum(status_counts.values()),
                'active_recipients': status_counts.get('active', 0),
                'invalid_recipients': status_counts.get('invalid', 0),
                'unknown_recipients': status_counts.get('unknown', 0)
            }

        except Exception as e:
            self.logger.error(f"获取收件人分析失败: {str(e)}")
            return {}

    def get_valid_recipients(self, sender_email: str) -> List[str]:
        """获取有效的收件人列表（有自动回复的）"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT recipient_email
                FROM recipient_status
                WHERE sender_email = ? AND status = 'active' AND reply_count > 0
                ORDER BY last_reply_time DESC
            ''', (sender_email,))

            valid_recipients = [row[0] for row in cursor.fetchall()]
            conn.close()

            self.logger.info(f"找到 {len(valid_recipients)} 个有效收件人")
            return valid_recipients

        except Exception as e:
            self.logger.error(f"获取有效收件人失败: {str(e)}")
            return []

    def get_invalid_recipients(self, sender_email: str) -> List[str]:
        """获取无效的收件人列表（退信的）"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT recipient_email
                FROM recipient_status
                WHERE sender_email = ? AND (status = 'invalid' OR bounce_count >= 2)
                ORDER BY bounce_count DESC
            ''', (sender_email,))

            invalid_recipients = [row[0] for row in cursor.fetchall()]
            conn.close()

            self.logger.info(f"找到 {len(invalid_recipients)} 个无效收件人")
            return invalid_recipients

        except Exception as e:
            self.logger.error(f"获取无效收件人失败: {str(e)}")
            return []

    def monitor_replies_continuously(self, check_interval: int = 300, max_checks: int = 24):
        """持续监控自动回复（用于发送后的监控）"""
        self.logger.info(f"开始持续监控自动回复，检查间隔: {check_interval}秒，最大检查次数: {max_checks}")

        for i in range(max_checks):
            try:
                self.logger.info(f"第 {i+1}/{max_checks} 次检查自动回复...")
                replies = self.check_recent_replies(hours=2)  # 检查最近2小时的回复

                if replies:
                    self.logger.info(f"发现 {len(replies)} 个新的自动回复")
                    for reply in replies:
                        self.logger.info(f"  - {reply['recipient_email']}: {reply['reply_type']}")

                if i < max_checks - 1:  # 不是最后一次检查
                    self.logger.info(f"等待 {check_interval} 秒后进行下次检查...")
                    time.sleep(check_interval)

            except Exception as e:
                self.logger.error(f"监控过程中出错: {str(e)}")
                time.sleep(60)  # 出错时等待1分钟再继续

        self.logger.info("持续监控完成")

    def export_recipient_report(self, sender_email: str, filepath: str):
        """导出收件人状态报告"""
        try:
            analysis = self.get_recipient_analysis(sender_email)

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write("📧 收件人状态分析报告\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"生成时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"发件人: {sender_email}\n\n")

                f.write("📊 状态统计:\n")
                f.write("-" * 30 + "\n")
                f.write(f"总收件人数: {analysis.get('total_recipients', 0)}\n")
                f.write(f"活跃收件人: {analysis.get('active_recipients', 0)}\n")
                f.write(f"无效收件人: {analysis.get('invalid_recipients', 0)}\n")
                f.write(f"未知状态: {analysis.get('unknown_recipients', 0)}\n\n")

                f.write("🔍 最近的自动回复:\n")
                f.write("-" * 30 + "\n")
                for reply in analysis.get('recent_replies', []):
                    f.write(f"时间: {reply[2]}\n")
                    f.write(f"收件人: {reply[0]}\n")
                    f.write(f"类型: {reply[1]}\n")
                    f.write(f"主题: {reply[3]}\n")
                    f.write("-" * 20 + "\n")

                f.write("\n⚠️ 问题收件人:\n")
                f.write("-" * 30 + "\n")
                for recipient in analysis.get('problematic_recipients', []):
                    f.write(f"邮箱: {recipient[0]}\n")
                    f.write(f"状态: {recipient[1]}\n")
                    f.write(f"退信次数: {recipient[2]}\n")
                    f.write(f"最后回复: {recipient[3] or '无'}\n")
                    f.write("-" * 20 + "\n")

            self.logger.info(f"收件人报告已导出到: {filepath}")

        except Exception as e:
            self.logger.error(f"导出报告失败: {str(e)}")

    def cleanup_old_replies(self, days: int = 30):
        """清理旧的自动回复记录"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cutoff_date = (datetime.datetime.now() - datetime.timedelta(days=days)).isoformat()

            cursor.execute('''
                DELETE FROM auto_replies
                WHERE reply_time < ?
            ''', (cutoff_date,))

            deleted_count = cursor.rowcount
            conn.commit()
            conn.close()

            self.logger.info(f"清理了 {deleted_count} 条 {days} 天前的自动回复记录")

        except Exception as e:
            self.logger.error(f"清理旧记录失败: {str(e)}")

    def test_connection(self) -> bool:
        """测试IMAP连接"""
        try:
            mail = self.connect_imap()
            mail.select('INBOX')
            status, messages = mail.search(None, 'ALL')
            mail.close()
            mail.logout()

            if status == 'OK':
                self.logger.info("IMAP连接测试成功")
                return True
            else:
                self.logger.error("IMAP连接测试失败")
                return False

        except Exception as e:
            self.logger.error(f"IMAP连接测试失败: {str(e)}")
            return False

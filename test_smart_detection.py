#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能检测功能测试脚本
测试优化后的智能识别算法，确保准确区分真正的拒绝和业务通知
"""

import logging
import sys

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_smart_detection():
    """测试智能检测功能"""
    logger.info("🧪 开始测试优化后的智能检测功能...")
    
    try:
        from email_receiver import EmailReceiver
        
        # 创建测试接收器
        receiver = EmailReceiver("<EMAIL>", "test_password")
        
        # 测试案例
        test_cases = [
            {
                'name': '案例1：无虞文化 - 临时暂停（不应撤回）',
                'email': {
                    'subject': '感谢投稿',
                    'body': '''宝宝好，这里是无虞文化的拉拉，感谢投稿~

暂停收稿两天哦，12号和13号的稿子不审了哦，周六日的稿子会在周一上班审稿哦~不一个个回复了哦

无虞主收追妻火葬场、下沉世情（小程序黑岩风，强情绪快节奏）、悬疑猎奇、热点事件文......除不收的类型外基本都收的哦~

不收类型：古言、修仙、末世、兽人、欧美背景、双男主、百合、同人、不收知乎拒稿、发过开头的、暂时不收中短哦''',
                    'from': '<EMAIL>'
                },
                'expected': 'business_notice',
                'should_recall': False
            },
            {
                'name': '案例2：晴鲤文化 - 收稿确认（不应撤回）',
                'email': {
                    'subject': '投稿确认',
                    'body': '''你好呀~这里是晴鲤文化阿鲤，你的投稿已经收到啦，阿鲤正在加速审核哦！

晴鲤文化主收【知乎风】，千50，一审可立结，审核期1-3日。

如3日内未收到回复，那就是漏掉啦，速速加qq3842911634哦！

【收稿方向】近期收稿方向【伪装】、【大小姐】、【双强】、【团宠】，其他言情、世情也收。新梗/多梗叠加+强钩子or反转or强剧情，更易过稿。

【不收】小程序/敏感题材/同人作品

【拒绝】AI/仿写/中译中

*因近期邮箱稿件较多，目前暂停邮箱拒稿意见，仅回复过稿与否。若有需要，可加小窗询问。''',
                    'from': '<EMAIL>'
                },
                'expected': 'auto_reply',
                'should_recall': False
            },
            {
                'name': '案例3：明确拒绝（应该撤回）',
                'email': {
                    'subject': '请勿再发',
                    'body': '您好，我们不再接收此类投稿，请勿发送。谢谢。',
                    'from': '<EMAIL>'
                },
                'expected': 'rejection',
                'should_recall': True
            },
            {
                'name': '案例4：永久停止收稿（应该撤回）',
                'email': {
                    'subject': '停止收稿通知',
                    'body': '通知：本平台已停止收稿，请不要再发送稿件。',
                    'from': '<EMAIL>'
                },
                'expected': 'rejection',
                'should_recall': True
            },
            {
                'name': '案例5：退订请求（应该撤回）',
                'email': {
                    'subject': 'Unsubscribe',
                    'body': 'Please remove me from your mailing list. Stop sending emails.',
                    'from': '<EMAIL>'
                },
                'expected': 'rejection',
                'should_recall': True
            },
            {
                'name': '案例6：标准自动回复（不应撤回）',
                'email': {
                    'subject': 'Auto Reply',
                    'body': 'Thank you for your email. I am currently out of office and will respond when I return.',
                    'from': '<EMAIL>'
                },
                'expected': 'auto_reply',
                'should_recall': False
            }
        ]
        
        # 执行测试
        results = []
        for i, test_case in enumerate(test_cases, 1):
            logger.info(f"\n📝 测试 {i}: {test_case['name']}")
            
            is_auto, reply_type = receiver.is_auto_reply(test_case['email'])
            should_recall = reply_type == 'rejection'
            
            # 判断结果
            type_correct = reply_type == test_case['expected']
            recall_correct = should_recall == test_case['should_recall']
            overall_correct = type_correct and recall_correct
            
            result = {
                'test_name': test_case['name'],
                'detected_type': reply_type,
                'expected_type': test_case['expected'],
                'should_recall': should_recall,
                'expected_recall': test_case['should_recall'],
                'type_correct': type_correct,
                'recall_correct': recall_correct,
                'overall_correct': overall_correct
            }
            results.append(result)
            
            # 显示结果
            status = "✅" if overall_correct else "❌"
            logger.info(f"{status} 检测类型: {reply_type} (期望: {test_case['expected']})")
            logger.info(f"   是否撤回: {should_recall} (期望: {test_case['should_recall']})")
            
            if not overall_correct:
                if not type_correct:
                    logger.warning(f"   ⚠️ 类型检测错误")
                if not recall_correct:
                    logger.warning(f"   ⚠️ 撤回判断错误")
        
        # 统计结果
        total_tests = len(results)
        correct_tests = sum(1 for r in results if r['overall_correct'])
        type_accuracy = sum(1 for r in results if r['type_correct']) / total_tests * 100
        recall_accuracy = sum(1 for r in results if r['recall_correct']) / total_tests * 100
        overall_accuracy = correct_tests / total_tests * 100
        
        logger.info(f"\n📊 测试结果统计:")
        logger.info(f"   总测试数: {total_tests}")
        logger.info(f"   完全正确: {correct_tests}")
        logger.info(f"   类型识别准确率: {type_accuracy:.1f}%")
        logger.info(f"   撤回判断准确率: {recall_accuracy:.1f}%")
        logger.info(f"   整体准确率: {overall_accuracy:.1f}%")
        
        # 详细分析
        logger.info(f"\n🔍 详细分析:")
        for result in results:
            if not result['overall_correct']:
                logger.info(f"   ❌ {result['test_name']}")
                logger.info(f"      检测: {result['detected_type']} | 期望: {result['expected_type']}")
                logger.info(f"      撤回: {result['should_recall']} | 期望: {result['expected_recall']}")
        
        # 特别关注您提供的案例
        logger.info(f"\n🎯 您的案例测试结果:")
        case1_result = results[0]  # 无虞文化
        case2_result = results[1]  # 晴鲤文化
        
        logger.info(f"   案例1 (无虞文化): {case1_result['detected_type']} - {'✅ 正确' if case1_result['overall_correct'] else '❌ 错误'}")
        logger.info(f"   案例2 (晴鲤文化): {case2_result['detected_type']} - {'✅ 正确' if case2_result['overall_correct'] else '❌ 错误'}")
        
        if case1_result['overall_correct'] and case2_result['overall_correct']:
            logger.info(f"   🎉 您的案例都被正确识别，不会触发撤回！")
        else:
            logger.warning(f"   ⚠️ 需要进一步优化算法")
        
        return overall_accuracy >= 80  # 80%以上准确率算通过
        
    except Exception as e:
        logger.error(f"❌ 智能检测测试失败: {str(e)}")
        return False

def test_specific_scenarios():
    """测试特定场景"""
    logger.info("\n🎯 测试特定场景...")
    
    try:
        from email_receiver import EmailReceiver
        receiver = EmailReceiver("<EMAIL>", "test_password")
        
        # 边界案例测试
        edge_cases = [
            {
                'name': '暂停但有明确恢复时间',
                'body': '暂停收稿，下周一恢复，感谢投稿',
                'expected': 'business_notice'
            },
            {
                'name': '暂停但有联系方式',
                'body': '暂停收稿，有问题请联系QQ123456',
                'expected': 'business_notice'
            },
            {
                'name': '暂停且无任何积极信号',
                'body': '暂停收稿',
                'expected': 'rejection'
            },
            {
                'name': '明确说不再接收',
                'body': '不再接收投稿，请勿发送',
                'expected': 'rejection'
            }
        ]
        
        logger.info("🧪 边界案例测试:")
        for case in edge_cases:
            email_content = {
                'subject': 'Test',
                'body': case['body'],
                'from': '<EMAIL>'
            }
            
            is_auto, reply_type = receiver.is_auto_reply(email_content)
            correct = reply_type == case['expected']
            status = "✅" if correct else "❌"
            
            logger.info(f"   {status} {case['name']}: {reply_type} (期望: {case['expected']})")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 特定场景测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    logger.info("🚀 开始智能检测优化测试...")
    
    try:
        # 测试智能检测
        detection_success = test_smart_detection()
        
        # 测试特定场景
        scenario_success = test_specific_scenarios()
        
        if detection_success and scenario_success:
            logger.info("\n🎉 智能检测优化测试全部通过！")
            logger.info("✅ 系统现在能够准确区分:")
            logger.info("   📋 业务通知 (临时暂停) - 不撤回")
            logger.info("   ✉️ 收稿确认 - 不撤回") 
            logger.info("   🚫 明确拒绝 - 自动撤回")
            logger.info("   📤 自动回复 - 不撤回")
            return True
        else:
            logger.warning("\n⚠️ 部分测试未通过，需要进一步优化")
            return False
        
    except Exception as e:
        logger.error(f"❌ 测试执行失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

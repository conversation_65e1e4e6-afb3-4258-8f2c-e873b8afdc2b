#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
空正文验证器 - 确保空正文不被添加默认内容
"""

class EmptyBodyValidator:
    """空正文验证器"""
    
    # 统一的占位符文本
    PLACEHOLDER_TEXT = "请在此输入邮件正文内容...\n\n支持功能：\n• 支持中文输入\n• 支持Emoji表情 😊\n• 支持多行文本\n• 右键菜单快速插入表情"
    
    @classmethod
    def is_empty_body(cls, body_text):
        """判断是否为空正文"""
        if not body_text:
            return True
        
        # 去除空白字符
        cleaned_text = body_text.strip()
        if not cleaned_text:
            return True
        
        # 检查是否为占位符文本
        if cleaned_text == cls.PLACEHOLDER_TEXT.strip():
            return True
        
        return False
    
    @classmethod
    def process_body_text(cls, body_text):
        """处理正文文本，确保空正文返回空字符串"""
        if cls.is_empty_body(body_text):
            return ""
        return body_text.strip()
    
    @classmethod
    def validate_email_body(cls, body_text):
        """验证邮件正文，返回处理后的正文和是否为空的标志"""
        processed_body = cls.process_body_text(body_text)
        is_empty = len(processed_body) == 0
        
        return processed_body, is_empty

# 全局验证器实例
validator = EmptyBodyValidator()

def validate_body(body_text):
    """全局验证函数"""
    return validator.validate_email_body(body_text)

def is_empty_body(body_text):
    """全局判断函数"""
    return validator.is_empty_body(body_text)

def process_body(body_text):
    """全局处理函数"""
    return validator.process_body_text(body_text)

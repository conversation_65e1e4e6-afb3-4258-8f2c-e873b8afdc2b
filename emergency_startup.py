#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
紧急修复启动脚本
智能识别错误并自动修复，然后启动系统
"""

import os
import sys
import re
import ast
import logging
import subprocess
import traceback
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EmergencyFixer:
    """紧急修复器"""

    def __init__(self):
        self.main_file = "gui_main.py"
        self.max_fix_attempts = 5
        self.fix_history = []

    def detect_and_fix_errors(self):
        """检测并修复错误"""
        logger.info("🚨 启动紧急修复模式...")

        for attempt in range(self.max_fix_attempts):
            logger.info(f"🔧 修复尝试 {attempt + 1}/{self.max_fix_attempts}")

            # 检测语法错误
            syntax_ok, error_info = self._check_syntax()

            if syntax_ok:
                logger.info("✅ 语法检查通过，尝试启动系统...")
                return self._try_start_system()

            # 智能修复错误
            if not self._smart_fix_error(error_info):
                logger.error(f"❌ 第{attempt + 1}次修复失败")
                continue

        logger.error("❌ 达到最大修复尝试次数，修复失败")
        return False

    def _check_syntax(self):
        """检查语法"""
        try:
            result = subprocess.run([sys.executable, '-m', 'py_compile', self.main_file],
                                  capture_output=True, text=True, encoding='utf-8')

            if result.returncode == 0:
                return True, None
            else:
                return False, result.stderr

        except Exception as e:
            return False, str(e)

    def _smart_fix_error(self, error_info):
        """智能修复错误"""
        logger.info("🤖 智能分析错误...")

        try:
            # 解析错误信息
            error_patterns = [
                (r"line (\d+).*expected 'except' or 'finally' block", self._fix_incomplete_try),
                (r"line (\d+).*invalid syntax", self._fix_syntax_error),
                (r"line (\d+).*IndentationError", self._fix_indentation_error),
                (r"line (\d+).*unexpected EOF", self._fix_eof_error),
            ]

            for pattern, fix_func in error_patterns:
                match = re.search(pattern, error_info)
                if match:
                    line_num = int(match.group(1))
                    logger.info(f"🎯 识别到错误类型，修复第{line_num}行...")
                    return fix_func(line_num, error_info)

            # 通用修复
            return self._generic_fix(error_info)

        except Exception as e:
            logger.error(f"❌ 智能修复失败: {str(e)}")
            return False

    def _fix_incomplete_try(self, line_num, error_info):
        """修复不完整的try块"""
        try:
            with open(self.main_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            if line_num <= len(lines):
                target_line = lines[line_num - 1]

                # 如果是try语句缺少except
                if 'try:' in target_line:
                    indent = len(target_line) - len(target_line.lstrip())
                    except_block = ' ' * indent + "except Exception as e:\n"
                    except_block += ' ' * (indent + 4) + "logger.error(f'Error: {str(e)}')\n"
                    except_block += ' ' * (indent + 4) + "pass\n"

                    # 找到try块的结束位置
                    insert_pos = line_num
                    for i in range(line_num, len(lines)):
                        if lines[i].strip() and not lines[i].startswith(' ' * (indent + 4)):
                            insert_pos = i
                            break

                    lines.insert(insert_pos, except_block)

                    with open(self.main_file, 'w', encoding='utf-8') as f:
                        f.writelines(lines)

                    logger.info(f"✅ 修复了第{line_num}行的不完整try块")
                    self.fix_history.append(f"修复不完整try块(第{line_num}行)")
                    return True

            return False

        except Exception as e:
            logger.error(f"❌ 修复不完整try块失败: {str(e)}")
            return False

    def _fix_syntax_error(self, line_num, error_info):
        """修复语法错误"""
        try:
            with open(self.main_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            if line_num <= len(lines):
                target_line = lines[line_num - 1]

                # 常见语法错误修复
                fixes = [
                    # 修复缺少冒号
                    (r'(if|elif|else|for|while|def|class|try|except|finally|with)\s+[^:]*$',
                     lambda m: m.group(0) + ':'),
                    # 修复多余的逗号
                    (r',\s*$', ''),
                    # 修复括号不匹配
                    (r'\([^)]*$', lambda m: m.group(0) + ')'),
                ]

                original_line = target_line
                for pattern, replacement in fixes:
                    if isinstance(replacement, str):
                        target_line = re.sub(pattern, replacement, target_line)
                    else:
                        target_line = re.sub(pattern, replacement, target_line)

                if target_line != original_line:
                    lines[line_num - 1] = target_line

                    with open(self.main_file, 'w', encoding='utf-8') as f:
                        f.writelines(lines)

                    logger.info(f"✅ 修复了第{line_num}行的语法错误")
                    self.fix_history.append(f"修复语法错误(第{line_num}行)")
                    return True

            return False

        except Exception as e:
            logger.error(f"❌ 修复语法错误失败: {str(e)}")
            return False

    def _fix_indentation_error(self, line_num, error_info):
        """修复缩进错误"""
        try:
            with open(self.main_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            if line_num <= len(lines):
                # 分析前后行的缩进
                target_line = lines[line_num - 1]

                if line_num > 1:
                    prev_line = lines[line_num - 2]
                    prev_indent = len(prev_line) - len(prev_line.lstrip())

                    # 如果前一行以冒号结尾，当前行应该增加缩进
                    if prev_line.rstrip().endswith(':'):
                        correct_indent = prev_indent + 4
                    else:
                        correct_indent = prev_indent

                    # 修正缩进
                    fixed_line = ' ' * correct_indent + target_line.lstrip()
                    lines[line_num - 1] = fixed_line

                    with open(self.main_file, 'w', encoding='utf-8') as f:
                        f.writelines(lines)

                    logger.info(f"✅ 修复了第{line_num}行的缩进错误")
                    self.fix_history.append(f"修复缩进错误(第{line_num}行)")
                    return True

            return False

        except Exception as e:
            logger.error(f"❌ 修复缩进错误失败: {str(e)}")
            return False

    def _fix_eof_error(self, line_num, error_info):
        """修复EOF错误"""
        try:
            with open(self.main_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 检查是否有未闭合的括号、引号等
            open_brackets = content.count('(') - content.count(')')
            open_squares = content.count('[') - content.count(']')
            open_braces = content.count('{') - content.count('}')

            fixes = []
            if open_brackets > 0:
                fixes.extend([')'] * open_brackets)
            if open_squares > 0:
                fixes.extend([']'] * open_squares)
            if open_braces > 0:
                fixes.extend(['}'] * open_braces)

            if fixes:
                content += ''.join(fixes)

                with open(self.main_file, 'w', encoding='utf-8') as f:
                    f.write(content)

                logger.info(f"✅ 修复了EOF错误，添加了: {''.join(fixes)}")
                self.fix_history.append(f"修复EOF错误")
                return True

            return False

        except Exception as e:
            logger.error(f"❌ 修复EOF错误失败: {str(e)}")
            return False

    def _generic_fix(self, error_info):
        """通用修复"""
        logger.info("🔧 尝试通用修复...")

        try:
            # 运行批量修复脚本
            if os.path.exists('batch_fix_and_fortify.py'):
                result = subprocess.run([sys.executable, 'batch_fix_and_fortify.py'],
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    logger.info("✅ 批量修复脚本执行成功")
                    return True

            return False

        except Exception as e:
            logger.error(f"❌ 通用修复失败: {str(e)}")
            return False

    def _try_start_system(self):
        """尝试启动系统"""
        logger.info("🚀 尝试启动系统...")

        try:
            # 先进行导入测试
            result = subprocess.run([sys.executable, '-c', 'import gui_main'],
                                  capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                logger.info("✅ 系统导入测试通过")

                # 启动GUI系统
                logger.info("🖥️ 启动GUI系统...")
                subprocess.Popen([sys.executable, 'gui_main.py'])

                logger.info("🎉 系统启动成功！")
                return True
            else:
                logger.error(f"❌ 系统导入失败: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            logger.error("❌ 系统启动超时")
            return False
        except Exception as e:
            logger.error(f"❌ 系统启动失败: {str(e)}")
            return False

    def show_fix_summary(self):
        """显示修复摘要"""
        logger.info("\n" + "="*60)
        logger.info("📋 紧急修复摘要")
        logger.info("="*60)

        if self.fix_history:
            logger.info("✅ 已执行的修复:")
            for i, fix in enumerate(self.fix_history, 1):
                logger.info(f"   {i}. {fix}")
        else:
            logger.info("ℹ️ 未执行任何修复")

        logger.info("="*60)

def main():
    """主函数"""
    print("🚨 紧急修复启动脚本")
    print("="*50)

    fixer = EmergencyFixer()

    try:
        success = fixer.detect_and_fix_errors()
        fixer.show_fix_summary()

        if success:
            print("\n🎉 紧急修复成功，系统已启动！")
        else:
            print("\n❌ 紧急修复失败，请手动检查错误")
            print("💡 建议:")
            print("   1. 检查最新的备份文件")
            print("   2. 运行 python system_health_check.py")
            print("   3. 联系技术支持")

        return success

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断修复过程")
        return False
    except Exception as e:
        print(f"\n❌ 紧急修复过程出错: {str(e)}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件内容智能测试器
让您可以快速测试任何邮件内容的智能识别结果
"""

import sys
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_email_content(subject, body, from_addr="<EMAIL>"):
    """测试邮件内容的智能识别结果"""
    try:
        from email_receiver import EmailReceiver
        
        # 创建测试接收器
        receiver = EmailReceiver("<EMAIL>", "test_password")
        
        # 构建邮件内容
        email_content = {
            'subject': subject,
            'body': body,
            'from': from_addr
        }
        
        # 进行智能识别
        is_auto, reply_type = receiver.is_auto_reply(email_content)
        
        # 判断是否需要撤回
        should_recall = reply_type == 'rejection'
        
        return {
            'is_auto_reply': is_auto,
            'reply_type': reply_type,
            'should_recall': should_recall,
            'type_description': get_type_description(reply_type)
        }
        
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        return None

def get_type_description(reply_type):
    """获取回复类型的详细说明"""
    descriptions = {
        'rejection': '🚫 明确拒绝 - 会自动撤回邮件',
        'business_notice': '📋 业务通知 - 不会撤回，继续保持联系',
        'auto_reply': '📤 自动回复 - 不会撤回，标记为活跃',
        'bounce': '❌ 退信邮件 - 不会撤回，标记为无效',
        'normal': '📧 普通邮件 - 不会撤回'
    }
    return descriptions.get(reply_type, f'❓ 未知类型: {reply_type}')

def interactive_test():
    """交互式测试"""
    print("🧠 邮件内容智能测试器")
    print("=" * 50)
    print("输入邮件内容，系统会告诉您是否会触发自动撤回")
    print("输入 'quit' 或 'exit' 退出")
    print()
    
    while True:
        try:
            # 获取用户输入
            print("📝 请输入邮件主题（可选，直接回车跳过）:")
            subject = input("> ").strip()
            if subject.lower() in ['quit', 'exit']:
                break
            
            if not subject:
                subject = "测试邮件"
            
            print("\n📄 请输入邮件内容:")
            print("（输入完成后按回车，然后输入 'END' 结束）")
            
            body_lines = []
            while True:
                line = input("> ")
                if line.strip().upper() == 'END':
                    break
                if line.strip().lower() in ['quit', 'exit']:
                    return
                body_lines.append(line)
            
            body = '\n'.join(body_lines).strip()
            
            if not body:
                print("❌ 邮件内容不能为空，请重新输入\n")
                continue
            
            # 进行测试
            print("\n🔍 正在分析...")
            result = test_email_content(subject, body)
            
            if result:
                print("\n📊 分析结果:")
                print("=" * 30)
                print(f"📧 邮件主题: {subject}")
                print(f"📄 邮件内容: {body[:100]}{'...' if len(body) > 100 else ''}")
                print(f"🤖 识别类型: {result['reply_type']}")
                print(f"📋 详细说明: {result['type_description']}")
                print(f"🔄 是否撤回: {'是' if result['should_recall'] else '否'}")
                
                if result['should_recall']:
                    print("\n⚠️  注意：此邮件会触发自动撤回！")
                    print("   系统会自动发送撤回通知邮件")
                    print("   收件人状态会被标记为'已拒绝'")
                else:
                    print("\n✅ 此邮件不会触发撤回")
                    print("   系统会继续保持与此收件人的联系")
            else:
                print("❌ 分析失败，请检查系统配置")
            
            print("\n" + "=" * 50)
            print()
            
        except KeyboardInterrupt:
            print("\n\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {str(e)}")
            print()

def batch_test():
    """批量测试预设案例"""
    print("🧪 批量测试预设案例")
    print("=" * 50)
    
    test_cases = [
        {
            'name': '无虞文化案例',
            'subject': '感谢投稿',
            'body': '''宝宝好，这里是无虞文化的拉拉，感谢投稿~

暂停收稿两天哦，12号和13号的稿子不审了哦，周六日的稿子会在周一上班审稿哦~不一个个回复了哦

无虞主收追妻火葬场、下沉世情（小程序黑岩风，强情绪快节奏）、悬疑猎奇、热点事件文......除不收的类型外基本都收的哦~'''
        },
        {
            'name': '晴鲤文化案例',
            'subject': '投稿确认',
            'body': '''你好呀~这里是晴鲤文化阿鲤，你的投稿已经收到啦，阿鲤正在加速审核哦！

晴鲤文化主收【知乎风】，千50，一审可立结，审核期1-3日。

如3日内未收到回复，那就是漏掉啦，速速加qq3842911634哦！'''
        },
        {
            'name': '明确拒绝案例',
            'subject': '请勿再发',
            'body': '您好，我们不再接收此类投稿，请勿发送。谢谢。'
        },
        {
            'name': '退订请求案例',
            'subject': 'Unsubscribe',
            'body': 'Please remove me from your mailing list. Stop sending emails.'
        },
        {
            'name': '睿娱阅文化案例（温和拒绝）',
            'subject': '自动回复',
            'body': '''宝子好~我是睿娱阅文化的编辑玲子~

短篇暂时不对外收稿啦~发到邮箱里的宝子也别慌，我不会打开文件，咱们这边及时撤回就好。祝宝子们短篇在别处大麦~~'''
        },
        {
            'name': '要求撤稿案例（温和拒绝）',
            'subject': '暂停收稿通知',
            'body': '''很抱歉，我们家暂停收稿啦！已经投搞的宝子麻烦动动手指自行撤稿哦！

后续恢复收稿的话编编会改自动回复和名称哒！

最后，还是祝各位作者大大稿子大卖！期待以后的合作哦！'''
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n📝 测试案例 {i}: {case['name']}")
        print("-" * 30)
        
        result = test_email_content(case['subject'], case['body'])
        
        if result:
            print(f"🤖 识别类型: {result['reply_type']}")
            print(f"📋 说明: {result['type_description']}")
            print(f"🔄 是否撤回: {'是' if result['should_recall'] else '否'}")
        else:
            print("❌ 测试失败")
    
    print("\n" + "=" * 50)

def main():
    """主函数"""
    if len(sys.argv) > 1:
        if sys.argv[1] == 'batch':
            batch_test()
        elif sys.argv[1] == 'test':
            if len(sys.argv) >= 3:
                # 命令行测试模式
                subject = sys.argv[2] if len(sys.argv) > 2 else "测试"
                body = sys.argv[3] if len(sys.argv) > 3 else ""
                
                if body:
                    result = test_email_content(subject, body)
                    if result:
                        print(f"类型: {result['reply_type']}")
                        print(f"撤回: {'是' if result['should_recall'] else '否'}")
                        print(f"说明: {result['type_description']}")
                    else:
                        print("测试失败")
                else:
                    print("请提供邮件内容")
            else:
                print("用法: python 邮件内容智能测试器.py test '主题' '内容'")
        else:
            print("用法:")
            print("  python 邮件内容智能测试器.py          # 交互式测试")
            print("  python 邮件内容智能测试器.py batch    # 批量测试")
            print("  python 邮件内容智能测试器.py test '主题' '内容'  # 命令行测试")
    else:
        interactive_test()

if __name__ == "__main__":
    main()

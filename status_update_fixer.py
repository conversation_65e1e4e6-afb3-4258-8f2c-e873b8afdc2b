#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
状态更新修复器 - 解决收件人状态更新问题
确保各个数据库之间的状态同步一致性
"""

import logging
import sqlite3
import datetime
import threading
import time
import json
import os
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass

@dataclass
class StatusUpdateTask:
    """状态更新任务"""
    recipient_email: str
    sender_email: str
    status: str
    source: str  # 来源：auto_reply, manual, system
    timestamp: str
    notes: str = ""

class StatusUpdateFixer:
    """状态更新修复器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.update_lock = threading.RLock()
        self.update_queue = []
        self.is_processing = False
        
        # 数据库路径
        self.db_paths = {
            'email_receiver': 'email_receiver.db',
            'recipient_quality': 'recipient_quality.db',
            'qq_anti_spam': 'qq_anti_spam.db',
            'anti_spam': 'anti_spam.db'
        }
        
        # 初始化修复器
        self._init_status_fixer()
        
        self.logger.info("状态更新修复器初始化完成")
    
    def _init_status_fixer(self):
        """初始化状态修复器"""
        try:
            # 检查并修复数据库结构
            self._check_and_fix_database_structure()
            
            # 启动状态同步检查
            self._start_sync_checker()
            
        except Exception as e:
            self.logger.error(f"❌ 初始化状态修复器失败: {str(e)}")
    
    def _check_and_fix_database_structure(self):
        """检查并修复数据库结构"""
        try:
            for db_name, db_path in self.db_paths.items():
                if os.path.exists(db_path):
                    self._fix_database_structure(db_name, db_path)
                else:
                    self.logger.warning(f"⚠️ 数据库不存在: {db_path}")
            
            self.logger.info("✅ 数据库结构检查完成")
            
        except Exception as e:
            self.logger.error(f"❌ 检查数据库结构失败: {str(e)}")
    
    def _fix_database_structure(self, db_name: str, db_path: str):
        """修复单个数据库结构"""
        try:
            conn = sqlite3.connect(db_path, timeout=30.0)
            cursor = conn.cursor()
            
            # 设置优化参数
            cursor.execute("PRAGMA journal_mode=WAL")
            cursor.execute("PRAGMA synchronous=NORMAL")
            cursor.execute("PRAGMA busy_timeout=30000")
            
            if db_name == 'email_receiver':
                # 修复email_receiver数据库
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS recipient_status (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        recipient_email TEXT NOT NULL,
                        sender_email TEXT NOT NULL,
                        last_sent_time TEXT,
                        last_reply_time TEXT,
                        reply_count INTEGER DEFAULT 0,
                        bounce_count INTEGER DEFAULT 0,
                        status TEXT DEFAULT 'unknown',
                        notes TEXT,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(recipient_email, sender_email)
                    )
                ''')
                
                # 添加索引
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_recipient_sender 
                    ON recipient_status(recipient_email, sender_email)
                ''')
                
            elif db_name == 'recipient_quality':
                # 修复recipient_quality数据库
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS recipient_status (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        recipient_email TEXT NOT NULL,
                        sender_email TEXT NOT NULL,
                        last_sent_time TEXT,
                        last_reply_time TEXT,
                        reply_count INTEGER DEFAULT 0,
                        bounce_count INTEGER DEFAULT 0,
                        status TEXT DEFAULT 'unknown',
                        quality_score REAL DEFAULT 50.0,
                        notes TEXT,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(recipient_email, sender_email)
                    )
                ''')
                
                # 添加质量评分索引
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_quality_score 
                    ON recipient_status(quality_score)
                ''')
            
            conn.commit()
            conn.close()
            
            self.logger.debug(f"✅ 数据库结构修复完成: {db_name}")
            
        except Exception as e:
            self.logger.error(f"❌ 修复数据库结构失败 {db_name}: {str(e)}")
    
    def update_recipient_status_safe(self, recipient_email: str, sender_email: str, 
                                   status: str, source: str = 'system', notes: str = "") -> bool:
        """安全更新收件人状态"""
        try:
            with self.update_lock:
                # 创建更新任务
                task = StatusUpdateTask(
                    recipient_email=recipient_email,
                    sender_email=sender_email,
                    status=status,
                    source=source,
                    timestamp=datetime.datetime.now().isoformat(),
                    notes=notes
                )
                
                # 添加到队列
                self.update_queue.append(task)
                
                # 如果没有在处理，启动处理
                if not self.is_processing:
                    self._start_processing()
                
                self.logger.info(f"✅ 状态更新任务已添加: {recipient_email} -> {status}")
                return True
                
        except Exception as e:
            self.logger.error(f"❌ 添加状态更新任务失败: {str(e)}")
            return False
    
    def _start_processing(self):
        """启动状态更新处理"""
        if self.is_processing:
            return
        
        self.is_processing = True
        processing_thread = threading.Thread(target=self._process_update_queue, daemon=True)
        processing_thread.start()
        
        self.logger.info("🔄 状态更新处理已启动")
    
    def _process_update_queue(self):
        """处理状态更新队列"""
        try:
            while self.update_queue:
                with self.update_lock:
                    if not self.update_queue:
                        break
                    
                    task = self.update_queue.pop(0)
                
                # 执行状态更新
                success = self._execute_status_update(task)
                
                if success:
                    self.logger.debug(f"✅ 状态更新成功: {task.recipient_email}")
                else:
                    self.logger.error(f"❌ 状态更新失败: {task.recipient_email}")
                
                # 添加延迟避免过于频繁
                time.sleep(0.1)
            
        except Exception as e:
            self.logger.error(f"❌ 处理状态更新队列失败: {str(e)}")
        finally:
            self.is_processing = False
            self.logger.debug("✅ 状态更新处理完成")
    
    def _execute_status_update(self, task: StatusUpdateTask) -> bool:
        """执行状态更新"""
        success_count = 0
        total_count = 0
        
        # 更新所有相关数据库
        for db_name, db_path in self.db_paths.items():
            if os.path.exists(db_path):
                total_count += 1
                if self._update_single_database(db_name, db_path, task):
                    success_count += 1
        
        # 记录更新历史
        self._record_update_history(task, success_count, total_count)
        
        return success_count > 0
    
    def _update_single_database(self, db_name: str, db_path: str, task: StatusUpdateTask) -> bool:
        """更新单个数据库"""
        max_retries = 3
        
        for attempt in range(max_retries):
            try:
                conn = sqlite3.connect(db_path, timeout=30.0)
                cursor = conn.cursor()
                
                # 设置优化参数
                cursor.execute("PRAGMA busy_timeout=30000")
                
                # 检查表是否存在
                cursor.execute('''
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name='recipient_status'
                ''')
                
                if not cursor.fetchone():
                    self.logger.warning(f"⚠️ 表不存在: {db_name}.recipient_status")
                    conn.close()
                    return False
                
                # 获取当前状态
                cursor.execute('''
                    SELECT reply_count, bounce_count, status FROM recipient_status
                    WHERE recipient_email = ? AND sender_email = ?
                ''', (task.recipient_email, task.sender_email))
                
                result = cursor.fetchone()
                
                if result:
                    # 更新现有记录
                    reply_count, bounce_count, current_status = result
                    
                    # 根据新状态调整计数
                    if task.status == 'active' and current_status != 'active':
                        reply_count += 1
                    elif task.status == 'invalid' and current_status != 'invalid':
                        bounce_count += 1
                    
                    cursor.execute('''
                        UPDATE recipient_status 
                        SET status = ?, reply_count = ?, bounce_count = ?, 
                            notes = ?, updated_at = ?
                        WHERE recipient_email = ? AND sender_email = ?
                    ''', (
                        task.status, reply_count, bounce_count,
                        task.notes, task.timestamp,
                        task.recipient_email, task.sender_email
                    ))
                else:
                    # 插入新记录
                    reply_count = 1 if task.status == 'active' else 0
                    bounce_count = 1 if task.status == 'invalid' else 0
                    
                    cursor.execute('''
                        INSERT INTO recipient_status 
                        (recipient_email, sender_email, status, reply_count, 
                         bounce_count, notes, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        task.recipient_email, task.sender_email, task.status,
                        reply_count, bounce_count, task.notes, task.timestamp
                    ))
                
                conn.commit()
                conn.close()
                
                self.logger.debug(f"✅ 数据库更新成功: {db_name}")
                return True
                
            except sqlite3.OperationalError as e:
                if "database is locked" in str(e) and attempt < max_retries - 1:
                    self.logger.warning(f"⚠️ 数据库锁定，重试 {attempt + 1}/{max_retries}: {db_name}")
                    time.sleep(0.5 * (attempt + 1))  # 递增延迟
                    continue
                else:
                    self.logger.error(f"❌ 数据库操作失败 {db_name}: {str(e)}")
                    return False
            except Exception as e:
                self.logger.error(f"❌ 更新数据库失败 {db_name}: {str(e)}")
                return False
        
        return False
    
    def _record_update_history(self, task: StatusUpdateTask, success_count: int, total_count: int):
        """记录更新历史"""
        try:
            history_file = 'status_update_history.json'
            
            history_record = {
                'recipient_email': task.recipient_email,
                'sender_email': task.sender_email,
                'status': task.status,
                'source': task.source,
                'timestamp': task.timestamp,
                'notes': task.notes,
                'success_count': success_count,
                'total_count': total_count,
                'success_rate': (success_count / total_count * 100) if total_count > 0 else 0
            }
            
            # 读取现有历史
            history = []
            if os.path.exists(history_file):
                try:
                    with open(history_file, 'r', encoding='utf-8') as f:
                        history = json.load(f)
                except:
                    history = []
            
            # 添加新记录
            history.append(history_record)
            
            # 保持最近1000条记录
            if len(history) > 1000:
                history = history[-1000:]
            
            # 保存历史
            with open(history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, ensure_ascii=False, indent=2)
            
        except Exception as e:
            self.logger.error(f"❌ 记录更新历史失败: {str(e)}")
    
    def _start_sync_checker(self):
        """启动同步检查器"""
        def sync_checker():
            while True:
                try:
                    time.sleep(300)  # 每5分钟检查一次
                    self._check_database_sync()
                except Exception as e:
                    self.logger.error(f"❌ 同步检查失败: {str(e)}")
        
        sync_thread = threading.Thread(target=sync_checker, daemon=True)
        sync_thread.start()
        
        self.logger.info("🔄 数据库同步检查器已启动")
    
    def _check_database_sync(self):
        """检查数据库同步状态"""
        try:
            # 获取所有数据库的状态数据
            all_statuses = {}
            
            for db_name, db_path in self.db_paths.items():
                if os.path.exists(db_path):
                    statuses = self._get_database_statuses(db_path)
                    all_statuses[db_name] = statuses
            
            # 检查不一致的状态
            inconsistencies = self._find_status_inconsistencies(all_statuses)
            
            if inconsistencies:
                self.logger.warning(f"⚠️ 发现 {len(inconsistencies)} 个状态不一致")
                self._fix_status_inconsistencies(inconsistencies)
            else:
                self.logger.debug("✅ 数据库状态同步正常")
            
        except Exception as e:
            self.logger.error(f"❌ 检查数据库同步失败: {str(e)}")
    
    def _get_database_statuses(self, db_path: str) -> Dict:
        """获取数据库中的状态数据"""
        try:
            conn = sqlite3.connect(db_path, timeout=30.0)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT recipient_email, sender_email, status, updated_at
                FROM recipient_status
            ''')
            
            statuses = {}
            for row in cursor.fetchall():
                key = f"{row[0]}|{row[1]}"  # recipient|sender
                statuses[key] = {
                    'status': row[2],
                    'updated_at': row[3]
                }
            
            conn.close()
            return statuses
            
        except Exception as e:
            self.logger.error(f"❌ 获取数据库状态失败: {str(e)}")
            return {}
    
    def _find_status_inconsistencies(self, all_statuses: Dict) -> List:
        """查找状态不一致"""
        inconsistencies = []
        
        try:
            # 获取所有唯一的收件人-发件人组合
            all_keys = set()
            for db_statuses in all_statuses.values():
                all_keys.update(db_statuses.keys())
            
            # 检查每个组合的状态一致性
            for key in all_keys:
                statuses = []
                for db_name, db_statuses in all_statuses.items():
                    if key in db_statuses:
                        statuses.append({
                            'db_name': db_name,
                            'status': db_statuses[key]['status'],
                            'updated_at': db_statuses[key]['updated_at']
                        })
                
                # 如果状态不一致
                if len(set(s['status'] for s in statuses)) > 1:
                    recipient_email, sender_email = key.split('|')
                    inconsistencies.append({
                        'recipient_email': recipient_email,
                        'sender_email': sender_email,
                        'statuses': statuses
                    })
            
        except Exception as e:
            self.logger.error(f"❌ 查找状态不一致失败: {str(e)}")
        
        return inconsistencies
    
    def _fix_status_inconsistencies(self, inconsistencies: List):
        """修复状态不一致"""
        try:
            for inconsistency in inconsistencies:
                # 选择最新的状态作为正确状态
                latest_status = max(inconsistency['statuses'], 
                                  key=lambda x: x['updated_at'])
                
                # 更新所有数据库到最新状态
                self.update_recipient_status_safe(
                    inconsistency['recipient_email'],
                    inconsistency['sender_email'],
                    latest_status['status'],
                    'sync_fix',
                    '自动同步修复'
                )
                
                self.logger.info(f"🔧 修复状态不一致: {inconsistency['recipient_email']} -> {latest_status['status']}")
            
        except Exception as e:
            self.logger.error(f"❌ 修复状态不一致失败: {str(e)}")
    
    def get_status_statistics(self) -> Dict:
        """获取状态统计信息"""
        try:
            stats = {
                'total_recipients': 0,
                'status_distribution': {},
                'database_counts': {},
                'sync_status': 'unknown'
            }
            
            # 统计各数据库的状态分布
            for db_name, db_path in self.db_paths.items():
                if os.path.exists(db_path):
                    db_stats = self._get_database_statistics(db_path)
                    stats['database_counts'][db_name] = db_stats
                    
                    # 累计状态分布
                    for status, count in db_stats.get('status_distribution', {}).items():
                        stats['status_distribution'][status] = stats['status_distribution'].get(status, 0) + count
            
            # 计算总收件人数（去重）
            all_recipients = set()
            for db_counts in stats['database_counts'].values():
                all_recipients.update(db_counts.get('recipients', []))
            
            stats['total_recipients'] = len(all_recipients)
            stats['sync_status'] = 'healthy'  # 简化状态
            
            return stats
            
        except Exception as e:
            self.logger.error(f"❌ 获取状态统计失败: {str(e)}")
            return {}
    
    def _get_database_statistics(self, db_path: str) -> Dict:
        """获取单个数据库的统计信息"""
        try:
            conn = sqlite3.connect(db_path, timeout=30.0)
            cursor = conn.cursor()
            
            # 状态分布
            cursor.execute('''
                SELECT status, COUNT(*) FROM recipient_status 
                GROUP BY status
            ''')
            status_distribution = dict(cursor.fetchall())
            
            # 收件人列表
            cursor.execute('''
                SELECT DISTINCT recipient_email FROM recipient_status
            ''')
            recipients = [row[0] for row in cursor.fetchall()]
            
            conn.close()
            
            return {
                'status_distribution': status_distribution,
                'recipients': recipients,
                'total_count': sum(status_distribution.values())
            }
            
        except Exception as e:
            self.logger.error(f"❌ 获取数据库统计失败: {str(e)}")
            return {}

# 全局状态更新修复器实例
_status_fixer = None

def get_status_fixer() -> StatusUpdateFixer:
    """获取全局状态更新修复器实例"""
    global _status_fixer
    if _status_fixer is None:
        _status_fixer = StatusUpdateFixer()
    return _status_fixer

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
空正文策略测试
验证主系统和队列系统都严格执行：不填写就是空白发送
"""

import sys
import os

def test_empty_body_policy():
    """测试空正文策略"""
    print("🧪 空正文策略测试")
    print("="*50)
    print("📋 用户要求：不填写就是空白发送，绝不添加任何默认内容")
    print()
    
    try:
        from empty_body_validator import enforce_empty_policy, is_empty_body
        
        test_cases = [
            {
                'input': '',
                'description': '完全空正文',
                'expected': '',
                'should_be_empty': True
            },
            {
                'input': '   \n\t  \n  ',
                'description': '只有空白字符',
                'expected': '',
                'should_be_empty': True
            },
            {
                'input': '请在此输入邮件正文内容...',
                'description': '简单占位符',
                'expected': '',
                'should_be_empty': True
            },
            {
                'input': '请在此输入邮件正文内容...\n\n支持功能：\n• 支持中文输入\n• 支持Emoji表情 😊\n• 支持多行文本\n• 右键菜单快速插入表情',
                'description': '完整占位符',
                'expected': '',
                'should_be_empty': True
            },
            {
                'input': '这是用户真实输入的内容',
                'description': '用户真实内容',
                'expected': '这是用户真实输入的内容',
                'should_be_empty': False
            },
            {
                'input': '   用户内容前后有空格   ',
                'description': '有空格的用户内容',
                'expected': '用户内容前后有空格',
                'should_be_empty': False
            }
        ]
        
        all_passed = True
        
        for i, case in enumerate(test_cases, 1):
            print(f"📝 测试用例 {i}: {case['description']}")
            
            # 测试空正文判断
            is_empty = is_empty_body(case['input'])
            print(f"   空正文判断: {is_empty} (期望: {case['should_be_empty']})")
            
            # 测试策略执行
            result = enforce_empty_policy(case['input'])
            print(f"   处理结果: '{result}' (期望: '{case['expected']}')")
            
            # 验证结果
            empty_correct = is_empty == case['should_be_empty']
            result_correct = result == case['expected']
            
            if empty_correct and result_correct:
                print(f"   ✅ 测试通过")
            else:
                print(f"   ❌ 测试失败")
                if not empty_correct:
                    print(f"      空判断错误: 得到 {is_empty}, 期望 {case['should_be_empty']}")
                if not result_correct:
                    print(f"      结果错误: 得到 '{result}', 期望 '{case['expected']}'")
                all_passed = False
            
            print()
        
        print("="*50)
        if all_passed:
            print("🎉 所有测试通过！空正文策略执行正确！")
            print("✅ 用户不填写 = 空白发送")
            print("✅ 绝不添加任何默认内容")
        else:
            print("❌ 部分测试失败，需要检查空正文策略实现")
        
        return all_passed
        
    except ImportError as e:
        print(f"❌ 导入空正文验证器失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 测试执行失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_empty_body_policy()
    sys.exit(0 if success else 1)

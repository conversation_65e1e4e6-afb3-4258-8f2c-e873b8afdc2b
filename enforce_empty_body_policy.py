#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强制执行空正文策略
确保主系统和队列系统都严格按照用户要求：
不填写就是空白发送，绝不添加任何默认内容
"""

import re
import os
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EmptyBodyPolicyEnforcer:
    """空正文策略执行器"""
    
    def __init__(self):
        self.main_file = "gui_main.py"
        self.email_sender_file = "email_sender.py"
        self.validator_file = "empty_body_validator.py"
        self.fixes_applied = []
    
    def enforce_empty_body_policy(self):
        """强制执行空正文策略"""
        logger.info("🎯 开始强制执行空正文策略...")
        logger.info("📋 用户要求：不填写就是空白发送，绝不添加任何默认内容")
        
        try:
            # 1. 更新空正文验证器
            self._update_empty_body_validator()
            
            # 2. 检查主系统的空正文处理
            self._check_main_system_empty_body()
            
            # 3. 检查队列系统的空正文处理
            self._check_queue_system_empty_body()
            
            # 4. 检查邮件发送器的空正文处理
            self._check_email_sender_empty_body()
            
            # 5. 验证所有占位符文本处理
            self._verify_placeholder_handling()
            
            # 6. 创建测试验证
            self._create_empty_body_test()
            
            logger.info("✅ 空正文策略强制执行完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 强制执行空正文策略失败: {str(e)}")
            return False
    
    def _update_empty_body_validator(self):
        """更新空正文验证器，确保严格的空正文策略"""
        logger.info("🔧 更新空正文验证器...")
        
        try:
            validator_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
空正文验证器 - 严格执行空正文策略
用户要求：不填写就是空白发送，绝不添加任何默认内容
"""

class EmptyBodyValidator:
    """空正文验证器 - 严格模式"""
    
    # 统一的占位符文本
    PLACEHOLDER_TEXT = "请在此输入邮件正文内容...\\n\\n支持功能：\\n• 支持中文输入\\n• 支持Emoji表情 😊\\n• 支持多行文本\\n• 右键菜单快速插入表情"
    
    @classmethod
    def is_empty_body(cls, body_text):
        """
        判断是否为空正文
        严格模式：任何非实质内容都视为空正文
        """
        if not body_text:
            return True
        
        # 去除所有空白字符
        cleaned_text = body_text.strip()
        if not cleaned_text:
            return True
        
        # 检查是否为占位符文本（任何变体）
        placeholder_variants = [
            cls.PLACEHOLDER_TEXT.strip(),
            "请在此输入邮件正文内容...",
            "请在此输入邮件正文内容...\\n\\n支持中文、Emoji表情 😊",
            "请在此输入邮件正文内容...\\n\\n支持功能：\\n• 支持中文输入\\n• 支持Emoji表情 😊\\n• 支持多行文本\\n• 右键菜单快速插入表情"
        ]
        
        for variant in placeholder_variants:
            if cleaned_text == variant.strip():
                return True
        
        # 检查是否只包含空白字符和换行符
        if not cleaned_text.replace('\\n', '').replace('\\r', '').replace('\\t', '').replace(' ', ''):
            return True
        
        return False
    
    @classmethod
    def process_body_text(cls, body_text):
        """
        处理正文文本
        严格模式：空正文必须返回空字符串，绝不添加任何默认内容
        """
        if cls.is_empty_body(body_text):
            return ""  # 严格返回空字符串，绝不添加任何内容
        
        # 对于非空正文，只去除前后空白，保持原始内容
        return body_text.strip()
    
    @classmethod
    def validate_email_body(cls, body_text):
        """
        验证邮件正文
        返回处理后的正文和是否为空的标志
        """
        processed_body = cls.process_body_text(body_text)
        is_empty = len(processed_body) == 0
        
        return processed_body, is_empty
    
    @classmethod
    def enforce_empty_policy(cls, body_text):
        """
        强制执行空正文策略
        确保用户不填写就是空白发送
        """
        if cls.is_empty_body(body_text):
            return ""  # 绝对的空字符串
        else:
            return body_text.strip()  # 保持用户的真实内容

# 全局验证器实例
validator = EmptyBodyValidator()

def validate_body(body_text):
    """全局验证函数"""
    return validator.validate_email_body(body_text)

def is_empty_body(body_text):
    """全局判断函数"""
    return validator.is_empty_body(body_text)

def process_body(body_text):
    """全局处理函数 - 严格模式"""
    return validator.enforce_empty_policy(body_text)

def enforce_empty_policy(body_text):
    """强制执行空正文策略"""
    return validator.enforce_empty_policy(body_text)
'''
            
            with open(self.validator_file, 'w', encoding='utf-8') as f:
                f.write(validator_code)
            
            logger.info("✅ 空正文验证器已更新为严格模式")
            self.fixes_applied.append("更新空正文验证器为严格模式")
            
        except Exception as e:
            logger.error(f"❌ 更新空正文验证器失败: {str(e)}")
    
    def _check_main_system_empty_body(self):
        """检查主系统的空正文处理"""
        logger.info("🔍 检查主系统空正文处理...")
        
        try:
            with open(self.main_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查发送邮件方法中的空正文处理
            send_methods = [
                'def send_emails(self)',
                'def send_single_email(',
                'def send_batch_emails('
            ]
            
            issues_found = []
            
            for method in send_methods:
                if method in content:
                    # 查找方法中的正文处理
                    method_start = content.find(method)
                    if method_start != -1:
                        # 查找下一个方法或类的开始
                        next_method = content.find('def ', method_start + len(method))
                        next_class = content.find('class ', method_start + len(method))
                        
                        method_end = min([pos for pos in [next_method, next_class, len(content)] if pos > method_start])
                        method_content = content[method_start:method_end]
                        
                        # 检查是否使用了统一的空正文验证器
                        if 'from empty_body_validator import' not in method_content:
                            issues_found.append(f"{method} 未使用统一的空正文验证器")
            
            if issues_found:
                logger.warning(f"⚠️ 主系统发现 {len(issues_found)} 个空正文处理问题")
                for issue in issues_found:
                    logger.warning(f"   • {issue}")
            else:
                logger.info("✅ 主系统空正文处理检查通过")
            
        except Exception as e:
            logger.error(f"❌ 检查主系统空正文处理失败: {str(e)}")
    
    def _check_queue_system_empty_body(self):
        """检查队列系统的空正文处理"""
        logger.info("🔍 检查队列系统空正文处理...")
        
        try:
            with open(self.main_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查队列相关方法
            queue_methods = [
                'def add_to_queue(self)',
                'def send_queue_emails(self)',
                'def process_queue_task('
            ]
            
            issues_found = []
            
            for method in queue_methods:
                if method in content:
                    method_start = content.find(method)
                    if method_start != -1:
                        # 查找方法内容
                        next_method = content.find('def ', method_start + len(method))
                        next_class = content.find('class ', method_start + len(method))
                        
                        method_end = min([pos for pos in [next_method, next_class, len(content)] if pos > method_start])
                        method_content = content[method_start:method_end]
                        
                        # 检查是否使用了统一的空正文验证器
                        if 'body.get(1.0, tk.END)' in method_content:
                            if 'from empty_body_validator import' not in method_content:
                                issues_found.append(f"{method} 未使用统一的空正文验证器")
            
            if issues_found:
                logger.warning(f"⚠️ 队列系统发现 {len(issues_found)} 个空正文处理问题")
                for issue in issues_found:
                    logger.warning(f"   • {issue}")
            else:
                logger.info("✅ 队列系统空正文处理检查通过")
            
        except Exception as e:
            logger.error(f"❌ 检查队列系统空正文处理失败: {str(e)}")
    
    def _check_email_sender_empty_body(self):
        """检查邮件发送器的空正文处理"""
        logger.info("🔍 检查邮件发送器空正文处理...")
        
        try:
            if os.path.exists(self.email_sender_file):
                with open(self.email_sender_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查_create_message方法
                if 'def _create_message(' in content:
                    method_start = content.find('def _create_message(')
                    if method_start != -1:
                        next_method = content.find('def ', method_start + 20)
                        method_end = next_method if next_method != -1 else len(content)
                        method_content = content[method_start:method_end]
                        
                        if 'from empty_body_validator import' in method_content:
                            logger.info("✅ 邮件发送器已使用统一的空正文验证器")
                        else:
                            logger.warning("⚠️ 邮件发送器未使用统一的空正文验证器")
                else:
                    logger.warning("⚠️ 未找到_create_message方法")
            else:
                logger.warning("⚠️ 邮件发送器文件不存在")
            
        except Exception as e:
            logger.error(f"❌ 检查邮件发送器空正文处理失败: {str(e)}")
    
    def _verify_placeholder_handling(self):
        """验证占位符文本处理"""
        logger.info("🔍 验证占位符文本处理...")
        
        try:
            with open(self.main_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找所有占位符文本定义
            placeholder_patterns = [
                r'placeholder_text = "([^"]*)"',
                r'PLACEHOLDER_TEXT = "([^"]*)"'
            ]
            
            placeholders_found = []
            for pattern in placeholder_patterns:
                matches = re.findall(pattern, content, re.DOTALL)
                placeholders_found.extend(matches)
            
            if placeholders_found:
                logger.info(f"📝 发现 {len(placeholders_found)} 个占位符文本定义")
                unique_placeholders = set(placeholders_found)
                if len(unique_placeholders) == 1:
                    logger.info("✅ 所有占位符文本定义一致")
                else:
                    logger.warning(f"⚠️ 发现 {len(unique_placeholders)} 个不同的占位符文本")
                    for i, placeholder in enumerate(unique_placeholders, 1):
                        logger.warning(f"   {i}. {placeholder[:50]}...")
            else:
                logger.info("ℹ️ 未发现硬编码的占位符文本")
            
        except Exception as e:
            logger.error(f"❌ 验证占位符文本处理失败: {str(e)}")
    
    def _create_empty_body_test(self):
        """创建空正文测试"""
        logger.info("🧪 创建空正文测试...")
        
        try:
            test_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
空正文策略测试
验证主系统和队列系统都严格执行：不填写就是空白发送
"""

import sys
import os

def test_empty_body_policy():
    """测试空正文策略"""
    print("🧪 空正文策略测试")
    print("="*50)
    print("📋 用户要求：不填写就是空白发送，绝不添加任何默认内容")
    print()
    
    try:
        from empty_body_validator import enforce_empty_policy, is_empty_body
        
        test_cases = [
            {
                'input': '',
                'description': '完全空正文',
                'expected': '',
                'should_be_empty': True
            },
            {
                'input': '   \\n\\t  \\n  ',
                'description': '只有空白字符',
                'expected': '',
                'should_be_empty': True
            },
            {
                'input': '请在此输入邮件正文内容...',
                'description': '简单占位符',
                'expected': '',
                'should_be_empty': True
            },
            {
                'input': '请在此输入邮件正文内容...\\n\\n支持功能：\\n• 支持中文输入\\n• 支持Emoji表情 😊\\n• 支持多行文本\\n• 右键菜单快速插入表情',
                'description': '完整占位符',
                'expected': '',
                'should_be_empty': True
            },
            {
                'input': '这是用户真实输入的内容',
                'description': '用户真实内容',
                'expected': '这是用户真实输入的内容',
                'should_be_empty': False
            },
            {
                'input': '   用户内容前后有空格   ',
                'description': '有空格的用户内容',
                'expected': '用户内容前后有空格',
                'should_be_empty': False
            }
        ]
        
        all_passed = True
        
        for i, case in enumerate(test_cases, 1):
            print(f"📝 测试用例 {i}: {case['description']}")
            
            # 测试空正文判断
            is_empty = is_empty_body(case['input'])
            print(f"   空正文判断: {is_empty} (期望: {case['should_be_empty']})")
            
            # 测试策略执行
            result = enforce_empty_policy(case['input'])
            print(f"   处理结果: '{result}' (期望: '{case['expected']}')")
            
            # 验证结果
            empty_correct = is_empty == case['should_be_empty']
            result_correct = result == case['expected']
            
            if empty_correct and result_correct:
                print(f"   ✅ 测试通过")
            else:
                print(f"   ❌ 测试失败")
                if not empty_correct:
                    print(f"      空判断错误: 得到 {is_empty}, 期望 {case['should_be_empty']}")
                if not result_correct:
                    print(f"      结果错误: 得到 '{result}', 期望 '{case['expected']}'")
                all_passed = False
            
            print()
        
        print("="*50)
        if all_passed:
            print("🎉 所有测试通过！空正文策略执行正确！")
            print("✅ 用户不填写 = 空白发送")
            print("✅ 绝不添加任何默认内容")
        else:
            print("❌ 部分测试失败，需要检查空正文策略实现")
        
        return all_passed
        
    except ImportError as e:
        print(f"❌ 导入空正文验证器失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 测试执行失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_empty_body_policy()
    sys.exit(0 if success else 1)
'''
            
            with open('test_empty_body_policy.py', 'w', encoding='utf-8') as f:
                f.write(test_code)
            
            logger.info("✅ 空正文策略测试已创建")
            self.fixes_applied.append("创建空正文策略测试")
            
        except Exception as e:
            logger.error(f"❌ 创建空正文测试失败: {str(e)}")
    
    def show_summary(self):
        """显示执行摘要"""
        logger.info("\n" + "="*60)
        logger.info("📋 空正文策略强制执行摘要")
        logger.info("="*60)
        
        logger.info("🎯 用户要求:")
        logger.info("   不管是主系统还是队列系统")
        logger.info("   邮件正文默认空白")
        logger.info("   只要用户不填写就是空白发送")
        logger.info("   绝不添加任何默认内容")
        
        logger.info("\n✅ 已执行的强化措施:")
        for fix in self.fixes_applied:
            logger.info(f"   • {fix}")
        
        logger.info("\n💡 验证方法:")
        logger.info("   1. 运行测试: python test_empty_body_policy.py")
        logger.info("   2. 主系统测试: 不填写正文直接发送")
        logger.info("   3. 队列系统测试: 添加空正文任务到队列")
        logger.info("   4. 确认收到的邮件正文完全为空")
        
        logger.info("="*60)

def main():
    """主函数"""
    print("🎯 空正文策略强制执行器")
    print("="*50)
    print("📋 用户要求：不填写就是空白发送，绝不添加任何默认内容")
    print()
    
    enforcer = EmptyBodyPolicyEnforcer()
    
    try:
        success = enforcer.enforce_empty_body_policy()
        enforcer.show_summary()
        
        if success:
            print("\n🎉 空正文策略强制执行成功！")
            print("💡 现在主系统和队列系统都严格执行：不填写就是空白发送")
        else:
            print("\n⚠️ 空正文策略强制执行完成，但可能有问题")
        
        return success
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断执行过程")
        return False
    except Exception as e:
        print(f"\n❌ 执行过程出错: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能系统快速启动脚本
"""

import os
import sys
import subprocess
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """主启动函数"""
    logger.info("🚀 启动智能邮件系统...")
    
    try:
        # 检查系统状态
        logger.info("📊 检查系统状态...")
        if os.path.exists('system_status_checker.py'):
            subprocess.run([sys.executable, 'system_status_checker.py'], check=True)
        
        # 启动主程序
        logger.info("🎯 启动主程序...")
        if os.path.exists('gui_main.py'):
            subprocess.run([sys.executable, 'gui_main.py'], check=True)
        else:
            logger.error("❌ 主程序文件不存在")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 启动失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终空正文修复测试
验证所有修复是否正确工作
"""

import logging
import sys

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_empty_body_validator():
    """测试空正文验证器"""
    logger.info("🧪 测试空正文验证器...")
    
    try:
        from empty_body_validator import validate_body, is_empty_body, process_body
        
        test_cases = [
            {
                'input': '',
                'description': '完全空正文',
                'expected_empty': True,
                'expected_output': ''
            },
            {
                'input': '   \n\t  \n  ',
                'description': '空白字符正文',
                'expected_empty': True,
                'expected_output': ''
            },
            {
                'input': '请在此输入邮件正文内容...\n\n支持功能：\n• 支持中文输入\n• 支持Emoji表情 😊\n• 支持多行文本\n• 右键菜单快速插入表情',
                'description': '占位符文本',
                'expected_empty': True,
                'expected_output': ''
            },
            {
                'input': '这是真实的邮件内容',
                'description': '真实内容',
                'expected_empty': False,
                'expected_output': '这是真实的邮件内容'
            },
            {
                'input': '   这是有前后空格的内容   ',
                'description': '有空格的真实内容',
                'expected_empty': False,
                'expected_output': '这是有前后空格的内容'
            }
        ]
        
        all_passed = True
        
        for i, case in enumerate(test_cases, 1):
            logger.info(f"\n📝 测试用例 {i}: {case['description']}")
            
            # 测试 is_empty_body
            is_empty = is_empty_body(case['input'])
            logger.info(f"   is_empty_body: {is_empty} (期望: {case['expected_empty']})")
            
            # 测试 process_body
            processed = process_body(case['input'])
            logger.info(f"   process_body: '{processed}' (期望: '{case['expected_output']}')")
            
            # 测试 validate_body
            validated, empty_flag = validate_body(case['input'])
            logger.info(f"   validate_body: '{validated}', empty={empty_flag}")
            
            # 验证结果
            empty_correct = is_empty == case['expected_empty']
            output_correct = processed == case['expected_output']
            validate_correct = validated == case['expected_output'] and empty_flag == case['expected_empty']
            
            case_passed = empty_correct and output_correct and validate_correct
            
            if case_passed:
                logger.info(f"   ✅ 测试通过")
            else:
                logger.error(f"   ❌ 测试失败")
                if not empty_correct:
                    logger.error(f"      空判断错误: 得到 {is_empty}, 期望 {case['expected_empty']}")
                if not output_correct:
                    logger.error(f"      输出错误: 得到 '{processed}', 期望 '{case['expected_output']}'")
                if not validate_correct:
                    logger.error(f"      验证错误: 得到 '{validated}', {empty_flag}, 期望 '{case['expected_output']}, {case['expected_empty']}'")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        logger.error(f"❌ 空正文验证器测试失败: {str(e)}")
        return False

def test_email_sender_integration():
    """测试邮件发送器集成"""
    logger.info("\n🧪 测试邮件发送器集成...")
    
    try:
        from email_sender import EmailSender
        
        # 创建测试发送器
        sender = EmailSender("<EMAIL>")
        
        test_bodies = [
            ('', '空正文'),
            ('   ', '空白字符'),
            ('请在此输入邮件正文内容...\n\n支持功能：\n• 支持中文输入\n• 支持Emoji表情 😊\n• 支持多行文本\n• 右键菜单快速插入表情', '占位符文本'),
            ('真实邮件内容', '真实内容')
        ]
        
        all_passed = True
        
        for body, description in test_bodies:
            logger.info(f"\n📧 测试 {description}:")
            logger.info(f"   输入: '{body}' (长度: {len(body)})")
            
            try:
                # 创建邮件消息
                msg = sender._create_message(
                    to_emails=["<EMAIL>"],
                    subject="测试邮件",
                    body=body
                )
                
                # 获取邮件正文
                email_body = None
                for part in msg.walk():
                    if part.get_content_type() == "text/plain":
                        email_body = part.get_payload(decode=True).decode('utf-8')
                        break
                
                logger.info(f"   输出: '{email_body}' (长度: {len(email_body) if email_body else 0})")
                
                # 验证结果
                if body.strip() == "" or body.strip() == "请在此输入邮件正文内容...\n\n支持功能：\n• 支持中文输入\n• 支持Emoji表情 😊\n• 支持多行文本\n• 右键菜单快速插入表情":
                    # 应该是空正文
                    if email_body == "":
                        logger.info("   ✅ 空正文正确处理")
                    else:
                        logger.error("   ❌ 空正文被添加了内容！")
                        all_passed = False
                else:
                    # 应该保持原内容
                    if email_body == body:
                        logger.info("   ✅ 非空正文正确保持")
                    else:
                        logger.warning(f"   ⚠️ 非空正文被修改: '{body}' -> '{email_body}'")
                
            except Exception as e:
                logger.error(f"   ❌ 创建邮件失败: {str(e)}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        logger.error(f"❌ 邮件发送器集成测试失败: {str(e)}")
        return False

def test_gui_integration():
    """测试GUI集成（模拟）"""
    logger.info("\n🧪 测试GUI集成（模拟）...")
    
    try:
        # 模拟GUI的占位符处理逻辑
        PLACEHOLDER_TEXT = "请在此输入邮件正文内容...\n\n支持功能：\n• 支持中文输入\n• 支持Emoji表情 😊\n• 支持多行文本\n• 右键菜单快速插入表情"
        
        def simulate_gui_processing(body_raw):
            """模拟GUI的正文处理"""
            try:
                from empty_body_validator import process_body
                return process_body(body_raw)
            except ImportError:
                # 回退到原有逻辑
                if body_raw == PLACEHOLDER_TEXT.strip():
                    return ""
                else:
                    return body_raw
        
        test_cases = [
            ('', '空正文'),
            ('   \n\t  ', '空白字符'),
            (PLACEHOLDER_TEXT, '占位符文本'),
            ('真实邮件内容', '真实内容')
        ]
        
        all_passed = True
        
        for body_raw, description in test_cases:
            logger.info(f"\n🖥️ GUI处理 {description}:")
            logger.info(f"   输入: '{body_raw}' (长度: {len(body_raw)})")
            
            processed = simulate_gui_processing(body_raw)
            logger.info(f"   输出: '{processed}' (长度: {len(processed)})")
            
            # 验证结果
            if description in ['空正文', '空白字符', '占位符文本']:
                if processed == "":
                    logger.info("   ✅ 空正文正确处理")
                else:
                    logger.error("   ❌ 空正文处理错误")
                    all_passed = False
            else:
                if processed == body_raw.strip():
                    logger.info("   ✅ 非空正文正确处理")
                else:
                    logger.error("   ❌ 非空正文处理错误")
                    all_passed = False
        
        return all_passed
        
    except Exception as e:
        logger.error(f"❌ GUI集成测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    logger.info("🚀 开始最终空正文修复测试...")
    
    try:
        # 测试空正文验证器
        validator_success = test_empty_body_validator()
        
        # 测试邮件发送器集成
        sender_success = test_email_sender_integration()
        
        # 测试GUI集成
        gui_success = test_gui_integration()
        
        # 总结结果
        logger.info("\n" + "="*60)
        logger.info("📊 测试结果总结:")
        logger.info(f"   空正文验证器: {'✅ 通过' if validator_success else '❌ 失败'}")
        logger.info(f"   邮件发送器集成: {'✅ 通过' if sender_success else '❌ 失败'}")
        logger.info(f"   GUI集成: {'✅ 通过' if gui_success else '❌ 失败'}")
        
        overall_success = validator_success and sender_success and gui_success
        
        if overall_success:
            logger.info("\n🎉 所有测试通过！空正文问题已完全修复！")
            logger.info("✅ 系统现在会正确处理空正文：")
            logger.info("   • 完全空的正文 → 发送空内容")
            logger.info("   • 只有空白字符 → 发送空内容")
            logger.info("   • 占位符文本 → 发送空内容")
            logger.info("   • 真实内容 → 发送原内容")
            
            logger.info("\n💡 使用建议:")
            logger.info("1. 重启GUI程序以应用所有修复")
            logger.info("2. 测试发送空正文邮件")
            logger.info("3. 确认不会添加任何默认内容")
            
            return True
        else:
            logger.warning("\n⚠️ 部分测试失败，需要进一步修复")
            return False
        
    except Exception as e:
        logger.error(f"❌ 测试执行失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

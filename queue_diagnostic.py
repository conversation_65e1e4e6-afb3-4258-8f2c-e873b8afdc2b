#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
队列系统诊断工具
检查队列数据一致性和显示问题
"""

import json
import os
import datetime

def diagnose_queue_system():
    """诊断队列系统"""
    print("🔍 队列系统诊断报告")
    print("=" * 50)
    
    # 检查队列文件
    queue_files = [
        'auto_email_queue.json',
        'email_queue_backup.json',
        'queue_tasks.json'
    ]
    
    total_tasks = 0
    file_tasks = {}
    
    for queue_file in queue_files:
        if os.path.exists(queue_file):
            try:
                with open(queue_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                if isinstance(data, list):
                    task_count = len(data)
                    file_tasks[queue_file] = {
                        'count': task_count,
                        'tasks': data
                    }
                    total_tasks += task_count
                    print(f"📄 {queue_file}: {task_count} 个任务")
                    
                    # 显示任务详情
                    for i, task in enumerate(data[:3]):  # 只显示前3个
                        status = task.get('status', 'unknown')
                        subject = task.get('subject', 'No Subject')[:30]
                        print(f"   {i+1}. [{status}] {subject}")
                    
                    if len(data) > 3:
                        print(f"   ... 还有 {len(data) - 3} 个任务")
                
            except Exception as e:
                print(f"❌ 读取 {queue_file} 失败: {str(e)}")
        else:
            print(f"📄 {queue_file}: 不存在")
    
    print(f"\n📊 总计: {total_tasks} 个任务")
    
    # 检查状态分布
    if file_tasks:
        all_tasks = []
        for file_data in file_tasks.values():
            all_tasks.extend(file_data['tasks'])
        
        status_count = {}
        for task in all_tasks:
            status = task.get('status', 'unknown')
            status_count[status] = status_count.get(status, 0) + 1
        
        print("\n📈 状态分布:")
        for status, count in status_count.items():
            print(f"   {status}: {count} 个")
    
    # 检查重复任务
    if total_tasks > 0:
        task_ids = []
        for file_data in file_tasks.values():
            for task in file_data['tasks']:
                task_id = task.get('id')
                if task_id:
                    task_ids.append(task_id)
        
        duplicate_ids = [tid for tid in set(task_ids) if task_ids.count(tid) > 1]
        if duplicate_ids:
            print(f"\n⚠️ 发现重复任务ID: {duplicate_ids}")
        else:
            print("\n✅ 没有重复任务")
    
    # 生成修复建议
    print("\n💡 修复建议:")
    if total_tasks == 0:
        print("   • 队列为空，这可能是正常的")
    elif len(file_tasks) > 1:
        print("   • 发现多个队列文件，建议合并")
    else:
        print("   • 队列数据看起来正常")
    
    return file_tasks

def merge_queue_files():
    """合并队列文件"""
    print("\n🔧 开始合并队列文件...")
    
    file_tasks = diagnose_queue_system()
    
    if len(file_tasks) <= 1:
        print("✅ 无需合并")
        return
    
    # 合并所有任务
    merged_tasks = []
    seen_ids = set()
    
    for file_name, file_data in file_tasks.items():
        for task in file_data['tasks']:
            task_id = task.get('id')
            if task_id not in seen_ids:
                merged_tasks.append(task)
                seen_ids.add(task_id)
            else:
                print(f"⚠️ 跳过重复任务: {task_id}")
    
    # 保存合并后的队列
    backup_file = f"queue_backup_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(backup_file, 'w', encoding='utf-8') as f:
        json.dump(merged_tasks, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 已合并 {len(merged_tasks)} 个任务到 {backup_file}")
    
    # 更新主队列文件
    with open('auto_email_queue.json', 'w', encoding='utf-8') as f:
        json.dump(merged_tasks, f, ensure_ascii=False, indent=2)
    
    print("✅ 主队列文件已更新")

if __name__ == "__main__":
    diagnose_queue_system()
    
    # 询问是否合并
    response = input("\n是否要合并队列文件? (y/n): ")
    if response.lower() == 'y':
        merge_queue_files()

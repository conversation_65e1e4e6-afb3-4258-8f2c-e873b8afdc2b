#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
空正文验证器 - 严格执行空正文策略
用户要求：不填写就是空白发送，绝不添加任何默认内容
"""

class EmptyBodyValidator:
    """空正文验证器 - 严格模式"""
    
    # 统一的占位符文本
    PLACEHOLDER_TEXT = "请在此输入邮件正文内容...\n\n支持功能：\n• 支持中文输入\n• 支持Emoji表情 😊\n• 支持多行文本\n• 右键菜单快速插入表情"
    
    @classmethod
    def is_empty_body(cls, body_text):
        """
        判断是否为空正文
        严格模式：任何非实质内容都视为空正文
        """
        if not body_text:
            return True
        
        # 去除所有空白字符
        cleaned_text = body_text.strip()
        if not cleaned_text:
            return True
        
        # 检查是否为占位符文本（任何变体）
        placeholder_variants = [
            cls.PLACEHOLDER_TEXT.strip(),
            "请在此输入邮件正文内容...",
            "请在此输入邮件正文内容...\n\n支持中文、Emoji表情 😊",
            "请在此输入邮件正文内容...\n\n支持功能：\n• 支持中文输入\n• 支持Emoji表情 😊\n• 支持多行文本\n• 右键菜单快速插入表情"
        ]
        
        for variant in placeholder_variants:
            if cleaned_text == variant.strip():
                return True
        
        # 检查是否只包含空白字符和换行符
        if not cleaned_text.replace('\n', '').replace('\r', '').replace('\t', '').replace(' ', ''):
            return True
        
        return False
    
    @classmethod
    def process_body_text(cls, body_text):
        """
        处理正文文本
        严格模式：空正文必须返回空字符串，绝不添加任何默认内容
        """
        if cls.is_empty_body(body_text):
            return ""  # 严格返回空字符串，绝不添加任何内容
        
        # 对于非空正文，只去除前后空白，保持原始内容
        return body_text.strip()
    
    @classmethod
    def validate_email_body(cls, body_text):
        """
        验证邮件正文
        返回处理后的正文和是否为空的标志
        """
        processed_body = cls.process_body_text(body_text)
        is_empty = len(processed_body) == 0
        
        return processed_body, is_empty
    
    @classmethod
    def enforce_empty_policy(cls, body_text):
        """
        强制执行空正文策略
        确保用户不填写就是空白发送
        """
        if cls.is_empty_body(body_text):
            return ""  # 绝对的空字符串
        else:
            return body_text.strip()  # 保持用户的真实内容

# 全局验证器实例
validator = EmptyBodyValidator()

def validate_body(body_text):
    """全局验证函数"""
    return validator.validate_email_body(body_text)

def is_empty_body(body_text):
    """全局判断函数"""
    return validator.is_empty_body(body_text)

def process_body(body_text):
    """全局处理函数 - 严格模式"""
    return validator.enforce_empty_policy(body_text)

def enforce_empty_policy(body_text):
    """强制执行空正文策略"""
    return validator.enforce_empty_policy(body_text)

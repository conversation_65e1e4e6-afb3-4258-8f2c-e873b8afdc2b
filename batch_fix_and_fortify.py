#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量修复和加固系统
1. 批量修复所有语法错误
2. 系统加固防止再次出现问题
3. 创建紧急修复启动脚本
"""

import re
import os
import sys
import ast
import logging
import subprocess
import datetime
import shutil
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BatchFixAndFortify:
    """批量修复和加固系统"""
    
    def __init__(self):
        self.backup_dir = f"backup_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.main_file = "gui_main.py"
        self.fixed_issues = []
        self.syntax_errors = []
        
    def create_backup(self):
        """创建备份"""
        logger.info("📦 创建系统备份...")
        
        try:
            os.makedirs(self.backup_dir, exist_ok=True)
            
            # 备份主要文件
            important_files = [
                "gui_main.py",
                "email_sender.py", 
                "empty_body_validator.py",
                "config.json"
            ]
            
            for file in important_files:
                if os.path.exists(file):
                    shutil.copy2(file, os.path.join(self.backup_dir, file))
                    logger.info(f"✅ 已备份: {file}")
            
            logger.info(f"✅ 备份完成，保存在: {self.backup_dir}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建备份失败: {str(e)}")
            return False
    
    def detect_syntax_errors(self):
        """检测语法错误"""
        logger.info("🔍 检测语法错误...")
        
        try:
            result = subprocess.run([sys.executable, '-m', 'py_compile', self.main_file], 
                                  capture_output=True, text=True, encoding='utf-8')
            
            if result.returncode == 0:
                logger.info("✅ 未发现语法错误")
                return []
            else:
                # 解析错误信息
                error_lines = result.stderr.strip().split('\n')
                errors = []
                
                for line in error_lines:
                    if 'line' in line and 'SyntaxError' in line:
                        # 提取行号和错误类型
                        match = re.search(r'line (\d+)', line)
                        if match:
                            line_num = int(match.group(1))
                            error_type = line.split('SyntaxError: ')[-1] if 'SyntaxError: ' in line else 'Unknown'
                            errors.append({
                                'line': line_num,
                                'type': error_type,
                                'message': line
                            })
                
                self.syntax_errors = errors
                logger.info(f"🔍 发现 {len(errors)} 个语法错误")
                for error in errors:
                    logger.info(f"   第{error['line']}行: {error['type']}")
                
                return errors
                
        except Exception as e:
            logger.error(f"❌ 检测语法错误失败: {str(e)}")
            return []
    
    def fix_try_block_errors(self):
        """修复try块语法错误"""
        logger.info("🔧 修复try块语法错误...")
        
        try:
            with open(self.main_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            fixes_made = 0
            
            # 模式1: 修复不完整的try块
            pattern1 = r'(\s+try:\s*\n(?:\s+[^#\n][^\n]*\n)*)\s+# 使用统一的空正文验证器\s*\n\s+try:'
            
            def fix_pattern1(match):
                nonlocal fixes_made
                original = match.group(0)
                # 在第一个try块后正确缩进
                fixed = re.sub(r'(\s+try:\s*\n(?:\s+[^#\n][^\n]*\n)*)\s+# 使用统一的空正文验证器\s*\n\s+try:',
                             r'\1\n            # 使用统一的空正文验证器\n            try:', original)
                fixes_made += 1
                return fixed
            
            content = re.sub(pattern1, fix_pattern1, content, flags=re.MULTILINE)
            
            # 模式2: 修复缺少except的try块
            pattern2 = r'(\s+try:\s*\n(?:\s+[^#\n][^\n]*\n)*?)(\s+def\s+|\s+class\s+|\s+@|\Z)'
            
            def fix_pattern2(match):
                nonlocal fixes_made
                try_block = match.group(1)
                next_part = match.group(2)
                
                # 检查是否已有except或finally
                if 'except' not in try_block and 'finally' not in try_block:
                    # 添加通用except块
                    indent = re.search(r'^(\s+)try:', try_block, re.MULTILINE)
                    if indent:
                        indent_str = indent.group(1)
                        except_block = f"\n{indent_str}except Exception as e:\n{indent_str}    logger.error(f'Error: {{str(e)}}')\n{indent_str}    pass\n"
                        fixes_made += 1
                        return try_block + except_block + next_part
                
                return match.group(0)
            
            content = re.sub(pattern2, fix_pattern2, content, flags=re.MULTILINE | re.DOTALL)
            
            # 模式3: 修复特定的空正文验证器问题
            problematic_patterns = [
                # 修复auto_retrieve_suggestions方法
                (r'(def auto_retrieve_suggestions\(self\):\s*"""[^"]*"""\s*try:\s*\n(?:\s+[^\n]*\n)*?)\s+# 使用统一的空正文验证器\s*\n\s+try:',
                 r'\1\n            # 使用统一的空正文验证器\n            try:'),
                
                # 修复debug_similarity_analysis方法
                (r'(def debug_similarity_analysis\(self\):\s*"""[^"]*"""\s*try:\s*\n(?:\s+[^\n]*\n)*?)\s+# 使用统一的空正文验证器\s*\n\s+try:',
                 r'\1\n            # 使用统一的空正文验证器\n            try:'),
            ]
            
            for pattern, replacement in problematic_patterns:
                if re.search(pattern, content, re.MULTILINE | re.DOTALL):
                    content = re.sub(pattern, replacement, content, flags=re.MULTILINE | re.DOTALL)
                    fixes_made += 1
            
            if fixes_made > 0:
                with open(self.main_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                logger.info(f"✅ 修复了 {fixes_made} 个try块语法错误")
                self.fixed_issues.append(f"修复了 {fixes_made} 个try块语法错误")
                return True
            else:
                logger.info("ℹ️ 未发现需要修复的try块错误")
                return True
                
        except Exception as e:
            logger.error(f"❌ 修复try块错误失败: {str(e)}")
            return False
    
    def fix_indentation_errors(self):
        """修复缩进错误"""
        logger.info("🔧 修复缩进错误...")
        
        try:
            with open(self.main_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            fixed_lines = []
            fixes_made = 0
            
            for i, line in enumerate(lines):
                # 检查常见的缩进问题
                if line.strip().startswith('# 使用统一的空正文验证器'):
                    # 确保正确的缩进
                    if i > 0:
                        prev_line = lines[i-1]
                        if 'try:' in prev_line:
                            # 获取try的缩进级别
                            try_indent = len(prev_line) - len(prev_line.lstrip())
                            correct_indent = ' ' * (try_indent + 4)
                            if not line.startswith(correct_indent):
                                line = correct_indent + line.lstrip()
                                fixes_made += 1
                
                fixed_lines.append(line)
            
            if fixes_made > 0:
                with open(self.main_file, 'w', encoding='utf-8') as f:
                    f.writelines(fixed_lines)
                
                logger.info(f"✅ 修复了 {fixes_made} 个缩进错误")
                self.fixed_issues.append(f"修复了 {fixes_made} 个缩进错误")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 修复缩进错误失败: {str(e)}")
            return False
    
    def fix_queue_system_consistency(self):
        """修复队列系统一致性"""
        logger.info("🔧 修复队列系统一致性...")
        
        try:
            with open(self.main_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            fixes_made = 0
            
            # 确保队列系统使用主系统的所有功能
            queue_fixes = [
                # 队列发送使用主系统的EmailSender
                (r'def send_queue_emails\(self\):[^}]*?EmailSender\([^)]*\)',
                 lambda m: m.group(0).replace('EmailSender(', 'self.email_sender or EmailSender(')),
                
                # 队列空正文处理使用统一验证器
                (r'body = [^.]*\.get\(1\.0, tk\.END\)\.strip\(\)',
                 '''body_raw = self.body.get(1.0, tk.END)
            try:
                from empty_body_validator import process_body
                body = process_body(body_raw)
            except ImportError:
                body = body_raw.strip()'''),
            ]
            
            for pattern, replacement in queue_fixes:
                if isinstance(replacement, str):
                    if re.search(pattern, content):
                        content = re.sub(pattern, replacement, content)
                        fixes_made += 1
            
            if fixes_made > 0:
                with open(self.main_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                logger.info(f"✅ 修复了 {fixes_made} 个队列系统一致性问题")
                self.fixed_issues.append(f"修复了队列系统一致性问题")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 修复队列系统一致性失败: {str(e)}")
            return False
    
    def fortify_system(self):
        """系统加固"""
        logger.info("🛡️ 开始系统加固...")
        
        try:
            # 1. 添加错误处理装饰器
            self._add_error_handling_decorator()
            
            # 2. 添加语法检查钩子
            self._add_syntax_check_hooks()
            
            # 3. 创建系统健康检查
            self._create_health_check()
            
            logger.info("✅ 系统加固完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 系统加固失败: {str(e)}")
            return False
    
    def _add_error_handling_decorator(self):
        """添加错误处理装饰器"""
        decorator_code = '''
def safe_execute(func):
    """安全执行装饰器"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"方法 {func.__name__} 执行失败: {str(e)}")
            import tkinter.messagebox as messagebox
            messagebox.showerror("系统错误", f"操作失败: {str(e)}")
            return None
    return wrapper
'''
        
        # 在文件开头添加装饰器
        with open(self.main_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'def safe_execute(' not in content:
            # 在import语句后添加装饰器
            import_end = content.find('\nclass EmailSenderGUI:')
            if import_end != -1:
                content = content[:import_end] + decorator_code + content[import_end:]
                
                with open(self.main_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                logger.info("✅ 添加了错误处理装饰器")
    
    def _add_syntax_check_hooks(self):
        """添加语法检查钩子"""
        # 这个功能在紧急修复脚本中实现
        pass
    
    def _create_health_check(self):
        """创建系统健康检查"""
        health_check_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统健康检查
定期检查系统状态和潜在问题
"""

import os
import sys
import ast
import logging
import subprocess
from datetime import datetime

def check_syntax(file_path):
    """检查文件语法"""
    try:
        result = subprocess.run([sys.executable, '-m', 'py_compile', file_path], 
                              capture_output=True, text=True)
        return result.returncode == 0, result.stderr
    except Exception as e:
        return False, str(e)

def check_imports(file_path):
    """检查导入依赖"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            tree = ast.parse(f.read())
        
        imports = []
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                imports.append(node.module)
        
        missing_imports = []
        for imp in imports:
            if imp:
                try:
                    __import__(imp)
                except ImportError:
                    missing_imports.append(imp)
        
        return len(missing_imports) == 0, missing_imports
    except Exception as e:
        return False, [str(e)]

def health_check():
    """执行健康检查"""
    print("🏥 系统健康检查")
    print("=" * 50)
    
    # 检查主文件
    files_to_check = ['gui_main.py', 'email_sender.py', 'empty_body_validator.py']
    
    all_healthy = True
    
    for file in files_to_check:
        if os.path.exists(file):
            print(f"\\n📄 检查文件: {file}")
            
            # 语法检查
            syntax_ok, syntax_error = check_syntax(file)
            if syntax_ok:
                print("  ✅ 语法检查: 通过")
            else:
                print(f"  ❌ 语法检查: 失败 - {syntax_error}")
                all_healthy = False
            
            # 导入检查
            import_ok, missing = check_imports(file)
            if import_ok:
                print("  ✅ 导入检查: 通过")
            else:
                print(f"  ❌ 导入检查: 缺少 {missing}")
                all_healthy = False
        else:
            print(f"\\n❌ 文件不存在: {file}")
            all_healthy = False
    
    print("\\n" + "=" * 50)
    if all_healthy:
        print("🎉 系统健康状态: 良好")
    else:
        print("⚠️ 系统健康状态: 需要修复")
    
    return all_healthy

if __name__ == "__main__":
    health_check()
'''
        
        with open('system_health_check.py', 'w', encoding='utf-8') as f:
            f.write(health_check_code)
        
        logger.info("✅ 创建了系统健康检查工具")

    def create_emergency_startup_script(self):
        """创建紧急修复启动脚本"""
        logger.info("🚨 创建紧急修复启动脚本...")

        try:
            emergency_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
紧急修复启动脚本
智能识别错误并自动修复，然后启动系统
"""

import os
import sys
import re
import ast
import logging
import subprocess
import traceback
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EmergencyFixer:
    """紧急修复器"""

    def __init__(self):
        self.main_file = "gui_main.py"
        self.max_fix_attempts = 5
        self.fix_history = []

    def detect_and_fix_errors(self):
        """检测并修复错误"""
        logger.info("🚨 启动紧急修复模式...")

        for attempt in range(self.max_fix_attempts):
            logger.info(f"🔧 修复尝试 {attempt + 1}/{self.max_fix_attempts}")

            # 检测语法错误
            syntax_ok, error_info = self._check_syntax()

            if syntax_ok:
                logger.info("✅ 语法检查通过，尝试启动系统...")
                return self._try_start_system()

            # 智能修复错误
            if not self._smart_fix_error(error_info):
                logger.error(f"❌ 第{attempt + 1}次修复失败")
                continue

        logger.error("❌ 达到最大修复尝试次数，修复失败")
        return False

    def _check_syntax(self):
        """检查语法"""
        try:
            result = subprocess.run([sys.executable, '-m', 'py_compile', self.main_file],
                                  capture_output=True, text=True, encoding='utf-8')

            if result.returncode == 0:
                return True, None
            else:
                return False, result.stderr

        except Exception as e:
            return False, str(e)

    def _smart_fix_error(self, error_info):
        """智能修复错误"""
        logger.info("🤖 智能分析错误...")

        try:
            # 解析错误信息
            error_patterns = [
                (r"line (\\d+).*expected 'except' or 'finally' block", self._fix_incomplete_try),
                (r"line (\\d+).*invalid syntax", self._fix_syntax_error),
                (r"line (\\d+).*IndentationError", self._fix_indentation_error),
                (r"line (\\d+).*unexpected EOF", self._fix_eof_error),
            ]

            for pattern, fix_func in error_patterns:
                match = re.search(pattern, error_info)
                if match:
                    line_num = int(match.group(1))
                    logger.info(f"🎯 识别到错误类型，修复第{line_num}行...")
                    return fix_func(line_num, error_info)

            # 通用修复
            return self._generic_fix(error_info)

        except Exception as e:
            logger.error(f"❌ 智能修复失败: {str(e)}")
            return False

    def _fix_incomplete_try(self, line_num, error_info):
        """修复不完整的try块"""
        try:
            with open(self.main_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            if line_num <= len(lines):
                target_line = lines[line_num - 1]

                # 如果是try语句缺少except
                if 'try:' in target_line:
                    indent = len(target_line) - len(target_line.lstrip())
                    except_block = ' ' * indent + "except Exception as e:\\n"
                    except_block += ' ' * (indent + 4) + "logger.error(f'Error: {str(e)}')\\n"
                    except_block += ' ' * (indent + 4) + "pass\\n"

                    # 找到try块的结束位置
                    insert_pos = line_num
                    for i in range(line_num, len(lines)):
                        if lines[i].strip() and not lines[i].startswith(' ' * (indent + 4)):
                            insert_pos = i
                            break

                    lines.insert(insert_pos, except_block)

                    with open(self.main_file, 'w', encoding='utf-8') as f:
                        f.writelines(lines)

                    logger.info(f"✅ 修复了第{line_num}行的不完整try块")
                    self.fix_history.append(f"修复不完整try块(第{line_num}行)")
                    return True

            return False

        except Exception as e:
            logger.error(f"❌ 修复不完整try块失败: {str(e)}")
            return False

    def _fix_syntax_error(self, line_num, error_info):
        """修复语法错误"""
        try:
            with open(self.main_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            if line_num <= len(lines):
                target_line = lines[line_num - 1]

                # 常见语法错误修复
                fixes = [
                    # 修复缺少冒号
                    (r'(if|elif|else|for|while|def|class|try|except|finally|with)\\s+[^:]*$',
                     lambda m: m.group(0) + ':'),
                    # 修复多余的逗号
                    (r',\\s*$', ''),
                    # 修复括号不匹配
                    (r'\\([^)]*$', lambda m: m.group(0) + ')'),
                ]

                original_line = target_line
                for pattern, replacement in fixes:
                    if isinstance(replacement, str):
                        target_line = re.sub(pattern, replacement, target_line)
                    else:
                        target_line = re.sub(pattern, replacement, target_line)

                if target_line != original_line:
                    lines[line_num - 1] = target_line

                    with open(self.main_file, 'w', encoding='utf-8') as f:
                        f.writelines(lines)

                    logger.info(f"✅ 修复了第{line_num}行的语法错误")
                    self.fix_history.append(f"修复语法错误(第{line_num}行)")
                    return True

            return False

        except Exception as e:
            logger.error(f"❌ 修复语法错误失败: {str(e)}")
            return False

    def _fix_indentation_error(self, line_num, error_info):
        """修复缩进错误"""
        try:
            with open(self.main_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            if line_num <= len(lines):
                # 分析前后行的缩进
                target_line = lines[line_num - 1]

                if line_num > 1:
                    prev_line = lines[line_num - 2]
                    prev_indent = len(prev_line) - len(prev_line.lstrip())

                    # 如果前一行以冒号结尾，当前行应该增加缩进
                    if prev_line.rstrip().endswith(':'):
                        correct_indent = prev_indent + 4
                    else:
                        correct_indent = prev_indent

                    # 修正缩进
                    fixed_line = ' ' * correct_indent + target_line.lstrip()
                    lines[line_num - 1] = fixed_line

                    with open(self.main_file, 'w', encoding='utf-8') as f:
                        f.writelines(lines)

                    logger.info(f"✅ 修复了第{line_num}行的缩进错误")
                    self.fix_history.append(f"修复缩进错误(第{line_num}行)")
                    return True

            return False

        except Exception as e:
            logger.error(f"❌ 修复缩进错误失败: {str(e)}")
            return False

    def _fix_eof_error(self, line_num, error_info):
        """修复EOF错误"""
        try:
            with open(self.main_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 检查是否有未闭合的括号、引号等
            open_brackets = content.count('(') - content.count(')')
            open_squares = content.count('[') - content.count(']')
            open_braces = content.count('{') - content.count('}')

            fixes = []
            if open_brackets > 0:
                fixes.extend([')'] * open_brackets)
            if open_squares > 0:
                fixes.extend([']'] * open_squares)
            if open_braces > 0:
                fixes.extend(['}'] * open_braces)

            if fixes:
                content += ''.join(fixes)

                with open(self.main_file, 'w', encoding='utf-8') as f:
                    f.write(content)

                logger.info(f"✅ 修复了EOF错误，添加了: {''.join(fixes)}")
                self.fix_history.append(f"修复EOF错误")
                return True

            return False

        except Exception as e:
            logger.error(f"❌ 修复EOF错误失败: {str(e)}")
            return False

    def _generic_fix(self, error_info):
        """通用修复"""
        logger.info("🔧 尝试通用修复...")

        try:
            # 运行批量修复脚本
            if os.path.exists('batch_fix_and_fortify.py'):
                result = subprocess.run([sys.executable, 'batch_fix_and_fortify.py'],
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    logger.info("✅ 批量修复脚本执行成功")
                    return True

            return False

        except Exception as e:
            logger.error(f"❌ 通用修复失败: {str(e)}")
            return False

    def _try_start_system(self):
        """尝试启动系统"""
        logger.info("🚀 尝试启动系统...")

        try:
            # 先进行导入测试
            result = subprocess.run([sys.executable, '-c', 'import gui_main'],
                                  capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                logger.info("✅ 系统导入测试通过")

                # 启动GUI系统
                logger.info("🖥️ 启动GUI系统...")
                subprocess.Popen([sys.executable, 'gui_main.py'])

                logger.info("🎉 系统启动成功！")
                return True
            else:
                logger.error(f"❌ 系统导入失败: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            logger.error("❌ 系统启动超时")
            return False
        except Exception as e:
            logger.error(f"❌ 系统启动失败: {str(e)}")
            return False

    def show_fix_summary(self):
        """显示修复摘要"""
        logger.info("\\n" + "="*60)
        logger.info("📋 紧急修复摘要")
        logger.info("="*60)

        if self.fix_history:
            logger.info("✅ 已执行的修复:")
            for i, fix in enumerate(self.fix_history, 1):
                logger.info(f"   {i}. {fix}")
        else:
            logger.info("ℹ️ 未执行任何修复")

        logger.info("="*60)

def main():
    """主函数"""
    print("🚨 紧急修复启动脚本")
    print("="*50)

    fixer = EmergencyFixer()

    try:
        success = fixer.detect_and_fix_errors()
        fixer.show_fix_summary()

        if success:
            print("\\n🎉 紧急修复成功，系统已启动！")
        else:
            print("\\n❌ 紧急修复失败，请手动检查错误")
            print("💡 建议:")
            print("   1. 检查最新的备份文件")
            print("   2. 运行 python system_health_check.py")
            print("   3. 联系技术支持")

        return success

    except KeyboardInterrupt:
        print("\\n⚠️ 用户中断修复过程")
        return False
    except Exception as e:
        print(f"\\n❌ 紧急修复过程出错: {str(e)}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
'''

            with open('emergency_startup.py', 'w', encoding='utf-8') as f:
                f.write(emergency_script)

            # 创建批处理启动文件
            batch_script = '''@echo off
echo 🚨 紧急修复启动脚本
echo ========================
python emergency_startup.py
pause
'''

            with open('emergency_startup.bat', 'w', encoding='utf-8') as f:
                f.write(batch_script)

            logger.info("✅ 创建了紧急修复启动脚本")
            logger.info("   - emergency_startup.py (Python脚本)")
            logger.info("   - emergency_startup.bat (批处理文件)")

            return True

        except Exception as e:
            logger.error(f"❌ 创建紧急修复启动脚本失败: {str(e)}")
            return False

    def run_batch_fix(self):
        """运行批量修复"""
        logger.info("🚀 开始批量修复和加固...")

        try:
            # 1. 创建备份
            if not self.create_backup():
                logger.error("❌ 备份失败，中止修复")
                return False

            # 2. 检测语法错误
            errors = self.detect_syntax_errors()

            # 3. 批量修复
            fix_success = True

            if errors:
                logger.info("🔧 开始批量修复...")

                # 修复try块错误
                if not self.fix_try_block_errors():
                    fix_success = False

                # 修复缩进错误
                if not self.fix_indentation_errors():
                    fix_success = False

                # 再次检测
                remaining_errors = self.detect_syntax_errors()
                if remaining_errors:
                    logger.warning(f"⚠️ 仍有 {len(remaining_errors)} 个语法错误未修复")
                    fix_success = False

            # 4. 修复队列系统一致性
            if not self.fix_queue_system_consistency():
                fix_success = False

            # 5. 系统加固
            if not self.fortify_system():
                fix_success = False

            # 6. 创建紧急修复脚本
            if not self.create_emergency_startup_script():
                fix_success = False

            # 7. 最终验证
            final_errors = self.detect_syntax_errors()

            if not final_errors:
                logger.info("🎉 批量修复和加固完成！")
                logger.info("✅ 所有语法错误已修复")
                logger.info("✅ 系统加固完成")
                logger.info("✅ 紧急修复脚本已创建")

                self._show_completion_summary()
                return True
            else:
                logger.warning("⚠️ 批量修复完成，但仍有语法错误")
                logger.info("💡 可以使用紧急修复脚本继续修复")
                return False

        except Exception as e:
            logger.error(f"❌ 批量修复失败: {str(e)}")
            return False

    def _show_completion_summary(self):
        """显示完成摘要"""
        logger.info("\n" + "="*60)
        logger.info("📋 批量修复和加固完成摘要")
        logger.info("="*60)

        logger.info("✅ 已完成的修复:")
        for fix in self.fixed_issues:
            logger.info(f"   • {fix}")

        logger.info("\n🛡️ 系统加固内容:")
        logger.info("   • 添加了错误处理装饰器")
        logger.info("   • 创建了系统健康检查工具")
        logger.info("   • 创建了紧急修复启动脚本")

        logger.info("\n📁 创建的文件:")
        logger.info("   • system_health_check.py - 系统健康检查")
        logger.info("   • emergency_startup.py - 紧急修复启动脚本")
        logger.info("   • emergency_startup.bat - 批处理启动文件")
        logger.info(f"   • {self.backup_dir}/ - 系统备份目录")

        logger.info("\n💡 使用建议:")
        logger.info("   1. 重启系统: python gui_main.py")
        logger.info("   2. 如果出现问题: python emergency_startup.py")
        logger.info("   3. 健康检查: python system_health_check.py")
        logger.info("   4. 双击 emergency_startup.bat 快速修复")

        logger.info("="*60)

def main():
    """主函数"""
    print("🚀 批量修复和加固系统")
    print("="*50)

    fixer = BatchFixAndFortify()

    try:
        success = fixer.run_batch_fix()

        if success:
            print("\n🎉 批量修复和加固成功完成！")
            print("💡 系统已加固，具备自动修复能力")
        else:
            print("\n⚠️ 批量修复完成，但可能仍有问题")
            print("💡 请使用紧急修复脚本继续修复")

        return success

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断修复过程")
        return False
    except Exception as e:
        print(f"\n❌ 批量修复过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

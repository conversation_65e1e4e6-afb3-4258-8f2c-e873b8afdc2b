# -*- coding: utf-8 -*-
"""
自动化邮件发送助手
"""

import smtplib
import os
import time
import logging
import random
import datetime
import uuid
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email.header import Header
from email import encoders
from typing import List, Optional, Callable
from config import SMTP_CONFIG, DEFAULT_SETTINGS, RATE_LIMIT, EMAIL_HEADERS
from batch_manager import BatchManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('email_sender.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class EmailSender:
    """邮件发送类"""
    
    def __init__(self, sender_email: str):
        """
        初始化邮件发送器
        
        Args:
            sender_email: 发送者邮箱地址
        """
        self.sender_email = sender_email
        self.smtp_config = SMTP_CONFIG.copy()
        self.smtp_config['username'] = sender_email
        self.logger = logging.getLogger(__name__)
        
    def _create_message(self, to_emails: List[str], subject: str,
                       body: str, attachments: Optional[List[str]] = None) -> MIMEMultipart:
        """
        创建邮件消息

        Args:
            to_emails: 收件人邮箱列表
            subject: 邮件主题
            body: 邮件正文
            attachments: 附件文件路径列表

        Returns:
            MIMEMultipart: 邮件消息对象
        """
        # 创建邮件对象
        msg = MIMEMultipart()

        # 设置邮件头 - 使用正确的编码处理中文字符
        from email.header import Header

        # 确保发送者邮箱格式正确，符合RFC5322标准
        if '<' not in self.sender_email and '>' not in self.sender_email:
            # 如果没有尖括号格式，添加标准格式
            sender_name = self.sender_email.split('@')[0]
            formatted_sender = f"{sender_name} <{self.sender_email}>"
        else:
            formatted_sender = self.sender_email

        msg['From'] = formatted_sender

        # 处理收件人列表，确保格式正确
        clean_emails = []
        for email in to_emails:
            # 移除可能的空白字符
            email = email.strip()
            # 验证邮箱格式
            if self._validate_email(email):
                clean_emails.append(email)
            else:
                self.logger.warning(f"跳过无效邮箱地址: {email}")

        if not clean_emails:
            raise ValueError("没有有效的收件人邮箱地址")

        msg['To'] = ', '.join(clean_emails)
        # 使用Header类正确编码主题，支持中文字符
        msg['Subject'] = Header(subject, DEFAULT_SETTINGS['charset']).encode()
        
        # 添加增强的邮件头以提高送达率
        self._add_enhanced_headers(msg)
            
        # 处理邮件正文 - 确保空正文不被添加默认内容
        try:
            from empty_body_validator import process_body
            processed_body = process_body(body)
        except ImportError:
            # 回退到简单处理
            processed_body = body.strip() if body else ""

        # 添加邮件正文 - 使用简单的纯文本格式
        msg.attach(MIMEText(processed_body, 'plain', DEFAULT_SETTINGS['charset']))
        
        # 添加附件
        if attachments:
            for file_path in attachments:
                self._add_attachment(msg, file_path)

        return msg

    def _validate_email(self, email: str) -> bool:
        """
        验证邮箱地址格式

        Args:
            email: 邮箱地址

        Returns:
            bool: 邮箱格式是否有效
        """
        if not email or '@' not in email:
            return False

        parts = email.split('@')
        if len(parts) != 2:
            return False

        local, domain = parts

        # 检查本地部分
        if not local or len(local) > 64:
            return False

        # 检查本地部分不能有连续的点
        if '..' in local:
            return False

        # 检查本地部分不能以点开头或结尾
        if local.startswith('.') or local.endswith('.'):
            return False

        # 检查域名部分
        if not domain or '.' not in domain or len(domain) > 255:
            return False

        # 检查域名不能以点开头或结尾
        if domain.startswith('.') or domain.endswith('.'):
            return False

        # 检查不能有连续的点
        if '..' in domain:
            return False

        return True

    def _add_enhanced_headers(self, msg: MIMEMultipart):
        """
        添加增强的邮件头以提高送达率

        Args:
            msg: 邮件消息对象
        """
        # 生成唯一的Message-ID
        domain = self.sender_email.split('@')[1]
        msg['Message-ID'] = f"<{uuid.uuid4()}@{domain}>"

        # 添加当前时间戳
        msg['Date'] = datetime.datetime.now().strftime('%a, %d %b %Y %H:%M:%S %z')

        # 生成随机IP（模拟真实发送环境）
        fake_ip = f"192.168.{random.randint(1, 254)}.{random.randint(1, 254)}"

        # 添加所有增强的邮件头
        for header_name, header_value in EMAIL_HEADERS.items():
            if header_value is None:
                # 动态生成的头部
                if header_name == 'Message-ID':
                    continue  # 已经设置
                elif header_name == 'Date':
                    continue  # 已经设置
                elif header_name == 'X-Originating-IP':
                    msg[header_name] = f"[{fake_ip}]"
            else:
                msg[header_name] = header_value

        self.logger.debug("已添加增强邮件头")

    def _add_attachment(self, msg: MIMEMultipart, file_path: str):
        """
        添加附件到邮件
        
        Args:
            msg: 邮件消息对象
            file_path: 附件文件路径
        """
        if not os.path.exists(file_path):
            self.logger.warning(f"附件文件不存在: {file_path}")
            return
            
        # 检查文件大小
        file_size = os.path.getsize(file_path)
        if file_size > DEFAULT_SETTINGS['max_attachment_size']:
            self.logger.warning(f"附件文件过大: {file_path} ({file_size} bytes)")
            return
            
        # 检查文件类型
        file_ext = os.path.splitext(file_path)[1].lower()
        if file_ext not in DEFAULT_SETTINGS['supported_file_types']:
            self.logger.warning(f"不支持的文件类型: {file_path}")
            
        try:
            with open(file_path, 'rb') as attachment:
                part = MIMEBase('application', 'octet-stream')
                part.set_payload(attachment.read())
                
            # 编码附件
            encoders.encode_base64(part)
            
            # 设置附件头
            filename = os.path.basename(file_path)
            part.add_header(
                'Content-Disposition',
                f'attachment; filename= {Header(filename, DEFAULT_SETTINGS["charset"]).encode()}'
            )
            
            msg.attach(part)
            self.logger.info(f"已添加附件: {filename}")
            
        except Exception as e:
            self.logger.error(f"添加附件失败 {file_path}: {str(e)}")
    
    def send_email(self, to_emails: List[str], subject: str, body: str,
                   attachments: Optional[List[str]] = None) -> bool:
        """
        发送邮件

        Args:
            to_emails: 收件人邮箱列表
            subject: 邮件主题
            body: 邮件正文
            attachments: 附件文件路径列表

        Returns:
            bool: 发送是否成功
        """
        try:
            self.logger.info(f"开始发送邮件 - 收件人: {', '.join(to_emails)}")
            self.logger.info(f"邮件主题: {subject}")
            if attachments:
                self.logger.info(f"附件数量: {len(attachments)}")

            # 创建邮件消息
            msg = self._create_message(to_emails, subject, body, attachments)

            # 连接SMTP服务器
            self.logger.info("正在连接SMTP服务器...")
            server = smtplib.SMTP(self.smtp_config['server'], self.smtp_config['port'])
            server.set_debuglevel(0)  # 设置为1可以看到详细调试信息

            if self.smtp_config['use_tls']:
                self.logger.info("启用TLS加密")
                server.starttls()

            # 登录
            self.logger.info("正在进行SMTP认证...")
            server.login(self.smtp_config['username'], self.smtp_config['password'])
            self.logger.info("SMTP服务器登录成功")

            # 发送邮件
            self.logger.info("正在发送邮件...")
            # 使用UTF-8编码处理邮件内容，避免中文字符编码错误
            try:
                text = msg.as_string()
            except UnicodeEncodeError:
                # 如果出现编码错误，使用bytes方式发送
                text = msg.as_bytes().decode('utf-8')
            server.sendmail(self.sender_email, to_emails, text)
            server.quit()

            self.logger.info(f"✓ 邮件发送成功! 收件人: {', '.join(to_emails)}")
            return True

        except smtplib.SMTPAuthenticationError as e:
            self.logger.error(f"✗ SMTP认证失败: {str(e)} - 请检查邮箱地址和授权码")
            return False
        except smtplib.SMTPRecipientsRefused as e:
            # 收件人地址被拒绝，通常表示邮箱不存在
            self.logger.error(f"✗ 收件人地址被拒绝: {', '.join(to_emails)} - 邮箱可能不存在")
            return False
        except smtplib.SMTPResponseException as e:
            # SMTP响应错误，可能是邮箱不存在或其他问题
            if e.smtp_code == 550:
                self.logger.error(f"✗ 邮箱不存在或被拒绝: {', '.join(to_emails)}")
            elif e.smtp_code == 551:
                self.logger.error(f"✗ 用户不在本地，邮箱无效: {', '.join(to_emails)}")
            elif e.smtp_code == 552:
                self.logger.error(f"✗ 邮箱存储空间不足: {', '.join(to_emails)}")
            elif e.smtp_code == 553:
                self.logger.error(f"✗ 邮箱地址格式错误: {', '.join(to_emails)}")
            else:
                self.logger.error(f"✗ SMTP响应错误 {e.smtp_code}: {str(e)}")
            return False
        except smtplib.SMTPDataError as e:
            self.logger.error(f"✗ 邮件数据错误: {str(e)}")
            return False
        except smtplib.SMTPConnectError as e:
            self.logger.error(f"✗ SMTP连接错误: {str(e)} - 请检查网络连接")
            return False
        except smtplib.SMTPServerDisconnected as e:
            self.logger.error(f"✗ SMTP服务器断开连接: {str(e)}")
            return False
        except Exception as e:
            self.logger.error(f"✗ 发送邮件时发生未知错误: {str(e)}")
            return False
    
    def send_multiple_emails(self, email_list: List[dict], delay: Optional[int] = None) -> dict:
        """
        批量发送邮件
        
        Args:
            email_list: 邮件信息列表，每个元素包含to_emails, subject, body, attachments
            delay: 邮件间隔时间（秒），默认使用配置中的值
            
        Returns:
            dict: 发送结果统计
        """
        if delay is None:
            delay = RATE_LIMIT['delay_between_emails']
            
        results = {'success': 0, 'failed': 0, 'errors': []}
        
        for i, email_info in enumerate(email_list):
            self.logger.info(f"正在发送第 {i+1}/{len(email_list)} 封邮件...")
            
            success = self.send_email(
                email_info['to_emails'],
                email_info['subject'],
                email_info['body'],
                email_info.get('attachments')
            )
            
            if success:
                results['success'] += 1
            else:
                results['failed'] += 1
                results['errors'].append(f"第 {i+1} 封邮件发送失败")
            
            # 添加延迟（除了最后一封邮件）
            if i < len(email_list) - 1:
                time.sleep(delay)

        return results

    def send_batch_emails(self, email_list: List[dict], send_mode: str = 'standard',
                         progress_callback: Optional[Callable] = None,
                         stop_callback: Optional[Callable] = None,
                         pause_callback: Optional[Callable] = None,
                         session_id: Optional[str] = None,
                         resume_from_progress: bool = False) -> dict:
        """
        批次发送邮件（增强版 - 支持断点继续）

        Args:
            email_list: 邮件信息列表
            send_mode: 发送模式 ('fast', 'standard', 'safe')
            progress_callback: 进度回调函数
            stop_callback: 停止检查回调函数
            pause_callback: 暂停检查回调函数
            session_id: 发送会话ID（用于断点继续）
            resume_from_progress: 是否从保存的进度恢复

        Returns:
            dict: 发送结果统计
        """
        # 初始化批次管理器
        batch_manager = BatchManager(send_mode)

        # 生成会话ID
        if session_id is None:
            session_id = f"session_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
        batch_manager.session_id = session_id

        # 检查是否可以恢复发送
        start_batch = 1
        start_email_index = 0
        total_success = 0
        total_failed = 0

        if resume_from_progress:
            can_resume, progress_data = batch_manager.can_resume()
            if can_resume:
                batch_manager.resume_from_progress(progress_data)
                start_batch = progress_data.get('current_batch', 1)
                start_email_index = progress_data.get('current_email_index', 0)
                total_success = progress_data.get('success_count', 0)
                total_failed = progress_data.get('failed_count', 0)

                self.logger.info(f"🔄 从断点恢复发送: 第{start_batch}批次, 第{start_email_index}封邮件")
            else:
                self.logger.info("❌ 无法恢复发送，将从头开始")
                batch_manager.clear_progress()

        # 计算批次信息
        batch_info = batch_manager.calculate_batches(len(email_list))

        self.logger.info(f"🚀 开始批次发送 - 模式: {send_mode}, 会话ID: {session_id}")
        self.logger.info(f"📊 总计: {batch_info['total_emails']} 封邮件, {batch_info['total_batches']} 个批次")
        self.logger.info(f"⏱️ 预计耗时: {batch_info['estimated_time_hours']:.1f} 小时")

        if resume_from_progress and start_email_index > 0:
            self.logger.info(f"🔄 断点继续: 从第{start_email_index}封邮件开始")

        # 发送统计
        errors = []
        current_email_index = start_email_index

        # 逐批次发送
        for batch_num in range(start_batch, batch_info['total_batches'] + 1):
            # 检查是否需要停止
            if stop_callback and stop_callback():
                self.logger.info("⏹️ 收到停止指令，保存进度并终止发送")
                batch_manager.save_progress(session_id, len(email_list), current_email_index,
                                          batch_num, total_success, total_failed)
                break

            # 检查是否需要暂停
            if pause_callback and pause_callback():
                self.logger.info("⏸️ 收到暂停指令，保存进度")
                batch_manager.save_progress(session_id, len(email_list), current_email_index,
                                          batch_num, total_success, total_failed)
                batch_manager.pause_sending()

                # 等待恢复信号
                while batch_manager.is_sending_paused():
                    if stop_callback and stop_callback():
                        self.logger.info("⏹️ 暂停期间收到停止指令")
                        return {
                            'success': total_success,
                            'failed': total_failed,
                            'errors': errors,
                            'batch_info': batch_info,
                            'send_mode': send_mode,
                            'paused': True,
                            'session_id': session_id
                        }
                    time.sleep(1)  # 每秒检查一次

                self.logger.info("▶️ 恢复发送")

            # 获取当前批次的邮件
            batch_emails = batch_manager.get_batch_emails(email_list, batch_num)
            batch_manager.log_batch_start(batch_num, len(batch_emails))

            # 发送当前批次
            batch_success = 0
            batch_failed = 0

            # 计算当前批次的起始索引
            batch_start_index = (batch_num - 1) * batch_manager.batch_size

            for i, email_info in enumerate(batch_emails, 1):
                # 计算全局邮件索引
                global_email_index = batch_start_index + i - 1

                # 如果是断点继续，跳过已发送的邮件
                if resume_from_progress and global_email_index < start_email_index:
                    continue

                current_email_index = global_email_index

                # 检查是否需要停止
                if stop_callback and stop_callback():
                    self.logger.info("⏹️ 收到停止指令，保存进度并终止发送")
                    batch_manager.save_progress(session_id, len(email_list), current_email_index,
                                              batch_num, total_success, total_failed)
                    break

                # 检查是否需要暂停
                if pause_callback and pause_callback():
                    self.logger.info("⏸️ 收到暂停指令，保存进度")
                    batch_manager.save_progress(session_id, len(email_list), current_email_index,
                                              batch_num, total_success, total_failed)
                    batch_manager.pause_sending()

                    # 等待恢复信号
                    while batch_manager.is_sending_paused():
                        if stop_callback and stop_callback():
                            self.logger.info("⏹️ 暂停期间收到停止指令")
                            return {
                                'success': total_success,
                                'failed': total_failed,
                                'errors': errors,
                                'batch_info': batch_info,
                                'send_mode': send_mode,
                                'paused': True,
                                'session_id': session_id
                            }
                        time.sleep(1)

                    self.logger.info("▶️ 恢复发送")

                self.logger.info(f"📤 批次 {batch_num}/{batch_info['total_batches']} - 第 {i}/{len(batch_emails)} 封")

                # 发送邮件
                success = self.send_email(
                    email_info['to_emails'],
                    email_info['subject'],
                    email_info['body'],
                    email_info.get('attachments')
                )

                if success:
                    batch_success += 1
                    total_success += 1
                else:
                    batch_failed += 1
                    total_failed += 1
                    errors.append(f"批次 {batch_num} 第 {i} 封邮件发送失败")

                # 更新进度
                if progress_callback:
                    progress_info = batch_manager.get_progress_info()
                    progress_info.update({
                        'current_email': current_email_index + 1,
                        'total_emails': batch_info['total_emails'],
                        'success_count': total_success,
                        'failed_count': total_failed,
                        'session_id': session_id
                    })
                    progress_callback(progress_info)

                # 每10封邮件保存一次进度
                if (current_email_index + 1) % 10 == 0:
                    batch_manager.save_progress(session_id, len(email_list), current_email_index + 1,
                                              batch_num, total_success, total_failed)

                # 批次内邮件间隔
                if i < len(batch_emails):
                    email_interval = batch_manager.get_email_interval()
                    self.logger.info(f"⏱️ 邮件间隔等待: {email_interval:.1f}秒")
                    time.sleep(email_interval)

            # 记录批次完成
            batch_manager.log_batch_complete(batch_num, batch_success, batch_failed)

            # 批次间隔等待
            should_wait, wait_minutes = batch_manager.should_wait_between_batches(batch_num)
            if should_wait:
                batch_manager.log_batch_wait(wait_minutes)

                # 分段等待，便于中途停止
                wait_seconds = wait_minutes * 60
                wait_step = 30  # 每30秒检查一次停止信号

                for step in range(0, int(wait_seconds), wait_step):
                    if stop_callback and stop_callback():
                        self.logger.info("⏹️ 等待期间收到停止指令")
                        break
                    time.sleep(min(wait_step, wait_seconds - step))

        # 发送完成，清除进度文件
        if total_success + total_failed >= len(email_list):
            batch_manager.clear_progress()
            self.logger.info("🎉 所有邮件发送完成，进度文件已清除")

        # 返回结果
        results = {
            'success': total_success,
            'failed': total_failed,
            'errors': errors,
            'batch_info': batch_info,
            'send_mode': send_mode,
            'session_id': session_id,
            'completed': total_success + total_failed >= len(email_list)
        }

        self.logger.info(f"🎉 批次发送完成 - 成功: {total_success}, 失败: {total_failed}")
        return results

2025-06-16 19:21:03,898 - __main__ - INFO - 🚀 开始智能改进功能测试...
2025-06-16 19:21:03,899 - __main__ - INFO - 🧪 开始测试智能自动回复检测...
2025-06-16 19:21:03,944 - email_receiver - INFO - 自动回复数据库初始化完成
2025-06-16 19:21:03,944 - email_receiver - INFO - 邮件接收器初始化完成 - 邮箱: <EMAIL>
2025-06-16 19:21:03,944 - email_receiver - WARNING - 🚨 检测到拒绝接收关键词: 暂停收稿
2025-06-16 19:21:03,944 - __main__ - INFO - ✅ 测试用例 1: 自动回复：暂停收稿 -> rejection
2025-06-16 19:21:03,944 - email_receiver - WARNING - 🚨 检测到拒绝接收关键词: 停止收稿
2025-06-16 19:21:03,945 - __main__ - INFO - ✅ 测试用例 2: Re: 投稿申请 -> rejection
2025-06-16 19:21:03,945 - email_receiver - INFO - 检测到自动回复关键词: auto reply
2025-06-16 19:21:03,945 - __main__ - INFO - ✅ 测试用例 3: Auto Reply -> auto_reply
2025-06-16 19:21:03,945 - email_receiver - INFO - 检测到退信关键词: delivery failure
2025-06-16 19:21:03,945 - __main__ - INFO - ✅ 测试用例 4: Delivery Failure -> bounce
2025-06-16 19:21:03,945 - email_receiver - INFO - 检测到短消息自动回复特征
2025-06-16 19:21:03,945 - __main__ - INFO - ❌ 测试用例 5: 普通回复 -> auto_reply
2025-06-16 19:21:03,946 - __main__ - INFO - 📊 智能检测准确率: 4/5 (80.0%)
2025-06-16 19:21:03,946 - __main__ - INFO - 🧪 开始测试智能撤回管理器...
2025-06-16 19:21:03,972 - intelligent_recall_manager - INFO - ✅ 智能撤回数据库初始化完成
2025-06-16 19:21:03,974 - intelligent_recall_manager - INFO - 智能撤回管理器初始化完成
2025-06-16 19:21:03,980 - intelligent_recall_manager - INFO - ✅ 撤回任务已添加: <EMAIL>
2025-06-16 19:21:03,981 - intelligent_recall_manager - INFO - 🔄 撤回任务处理已启动
2025-06-16 19:21:03,981 - __main__ - INFO - ✅ 撤回任务创建成功: <EMAIL>
2025-06-16 19:21:03,981 - intelligent_recall_manager - INFO - 📤 执行撤回任务: <EMAIL>
2025-06-16 19:21:03,987 - intelligent_recall_manager - INFO - ✅ 撤回任务已添加: <EMAIL>
2025-06-16 19:21:03,987 - __main__ - INFO - ✅ 撤回任务创建成功: <EMAIL>
2025-06-16 19:21:03,995 - intelligent_recall_manager - INFO - ✅ 撤回任务已添加: <EMAIL>
2025-06-16 19:21:03,995 - __main__ - INFO - ✅ 撤回任务创建成功: <EMAIL>
2025-06-16 19:21:03,998 - __main__ - INFO - 📊 撤回统计: {
  "task_statistics": {
    "pending": 3
  },
  "total_recalls": 0,
  "successful_recalls": null,
  "success_rate": 0,
  "pending_tasks": 2
}
2025-06-16 19:21:03,998 - __main__ - INFO - 🧪 开始测试状态更新修复器...
2025-06-16 19:21:04,013 - intelligent_recall_manager - ERROR - ❌ 无法获取授权码: <EMAIL>
2025-06-16 19:21:04,014 - intelligent_recall_manager - ERROR - ❌ 撤回邮件发送失败: <EMAIL>
2025-06-16 19:21:04,033 - status_update_fixer - INFO - ✅ 数据库结构检查完成
2025-06-16 19:21:04,033 - status_update_fixer - INFO - 🔄 数据库同步检查器已启动
2025-06-16 19:21:04,033 - status_update_fixer - INFO - 状态更新修复器初始化完成
2025-06-16 19:21:04,033 - status_update_fixer - INFO - 🔄 状态更新处理已启动
2025-06-16 19:21:04,033 - status_update_fixer - INFO - ✅ 状态更新任务已添加: <EMAIL> -> active
2025-06-16 19:21:04,035 - __main__ - INFO - ✅ 状态更新成功: <EMAIL> -> active
2025-06-16 19:21:04,035 - status_update_fixer - INFO - ✅ 状态更新任务已添加: <EMAIL> -> invalid
2025-06-16 19:21:04,035 - __main__ - INFO - ✅ 状态更新成功: <EMAIL> -> invalid
2025-06-16 19:21:04,035 - status_update_fixer - INFO - ✅ 状态更新任务已添加: <EMAIL> -> rejected
2025-06-16 19:21:04,035 - __main__ - INFO - ✅ 状态更新成功: <EMAIL> -> rejected
2025-06-16 19:21:04,037 - status_update_fixer - ERROR - ❌ 数据库操作失败 email_receiver: no such column: bounce_count
2025-06-16 19:21:04,047 - status_update_fixer - ERROR - ❌ 获取数据库统计失败: no such table: recipient_status
2025-06-16 19:21:04,049 - status_update_fixer - WARNING - ⚠️ 表不存在: qq_anti_spam.recipient_status
2025-06-16 19:21:04,049 - status_update_fixer - ERROR - ❌ 获取数据库统计失败: no such table: recipient_status
2025-06-16 19:21:04,049 - __main__ - INFO - 📊 状态统计: {
  "total_recipients": 5,
  "status_distribution": {
    "active": 5
  },
  "database_counts": {
    "email_receiver": {
      "status_distribution": {
        "active": 5
      },
      "recipients": [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
      ],
      "total_count": 5
    },
    "recipient_quality": {
      "status_distribution": {},
      "recipients": [],
      "total_count": 0
    },
    "qq_anti_spam": {},
    "anti_spam": {}
  },
  "sync_status": "healthy"
}
2025-06-16 19:21:04,050 - __main__ - INFO - 🧪 开始测试撤回邮件功能...
2025-06-16 19:21:04,050 - __main__ - INFO - ✅ 邮件发送器创建成功
2025-06-16 19:21:04,050 - __main__ - INFO - 📤 模拟撤回邮件发送:
2025-06-16 19:21:04,051 - __main__ - INFO -    收件人: <EMAIL>, <EMAIL>
2025-06-16 19:21:04,051 - __main__ - INFO -    主题: 测试撤回邮件
2025-06-16 19:21:04,051 - status_update_fixer - WARNING - ⚠️ 表不存在: anti_spam.recipient_status
2025-06-16 19:21:04,051 - __main__ - INFO -    内容长度: 20 字符
2025-06-16 19:21:04,051 - __main__ - INFO - 🧪 开始测试集成工作流程...
2025-06-16 19:21:04,051 - __main__ - INFO - 1️⃣ 模拟接收到拒绝回复...
2025-06-16 19:21:04,055 - email_receiver - INFO - 自动回复数据库初始化完成
2025-06-16 19:21:04,055 - email_receiver - INFO - 邮件接收器初始化完成 - 邮箱: <EMAIL>
2025-06-16 19:21:04,056 - email_receiver - WARNING - 🚨 检测到拒绝接收关键词: 暂停收稿
2025-06-16 19:21:04,056 - __main__ - INFO - 2️⃣ 智能检测结果: rejection (是否自动回复: True)
2025-06-16 19:21:04,056 - __main__ - INFO - 3️⃣ 检测到拒绝关键词，触发智能处理...
2025-06-16 19:21:04,056 - email_receiver - INFO - 🔄 开始智能自动撤回流程: <EMAIL>
2025-06-16 19:21:04,063 - intelligent_recall_manager - INFO - ✅ 撤回任务已添加: <EMAIL>
2025-06-16 19:21:04,063 - email_receiver - INFO - ✅ 智能撤回任务已创建: <EMAIL>
2025-06-16 19:21:04,063 - __main__ - INFO - 4️⃣ 自动撤回流程已触发
2025-06-16 19:21:04,063 - status_update_fixer - INFO - ✅ 状态更新任务已添加: <EMAIL> -> rejected
2025-06-16 19:21:04,063 - __main__ - INFO - 5️⃣ 收件人状态更新成功
2025-06-16 19:21:04,063 - __main__ - INFO - ✅ 集成工作流程测试完成
2025-06-16 19:21:04,063 - __main__ - INFO - 📋 生成测试报告...
2025-06-16 19:21:04,064 - __main__ - INFO - 📄 测试报告已保存: intelligent_improvements_test_report_20250616_192104.json
2025-06-16 19:21:04,065 - __main__ - INFO - 📊 测试摘要:
2025-06-16 19:21:04,065 - __main__ - INFO -    总测试数: 5
2025-06-16 19:21:04,065 - __main__ - INFO -    通过测试: 5
2025-06-16 19:21:04,065 - __main__ - INFO -    失败测试: 0
2025-06-16 19:21:04,065 - __main__ - INFO -    成功率: 100.0%
2025-06-16 19:21:04,065 - __main__ - INFO - 🎉 所有测试通过！智能改进功能正常工作

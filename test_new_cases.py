#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新案例的深度语义分析
验证系统能否正确识别需要撤回的温和拒绝
"""

import logging
import sys

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_new_cases():
    """测试用户提供的新案例"""
    logger.info("🧪 测试新案例的深度语义分析...")
    
    try:
        from email_receiver import EmailReceiver
        
        # 创建测试接收器
        receiver = EmailReceiver("<EMAIL>", "test_password")
        
        # 新的测试案例
        new_cases = [
            {
                'name': '案例1：睿娱阅文化 - 温和拒绝（应该撤回）',
                'email': {
                    'subject': '自动回复',
                    'body': '''宝子好~我是睿娱阅文化的编辑玲子~

短篇暂时不对外收稿啦~发到邮箱里的宝子也别慌，我不会打开文件，咱们这边及时撤回就好。祝宝子们短篇在别处大麦~~''',
                    'from': '<EMAIL>'
                },
                'expected': 'rejection',
                'should_recall': True,
                'reason': '明确说"不会打开文件"、"及时撤回就好"、"在别处大麦"'
            },
            {
                'name': '案例2：暂停收稿要求撤稿（应该撤回）',
                'email': {
                    'subject': '暂停收稿通知',
                    'body': '''很抱歉，我们家暂停收稿啦！已经投搞的宝子麻烦动动手指自行撤稿哦！

后续恢复收稿的话编编会改自动回复和名称哒！

最后，还是祝各位作者大大稿子大卖！期待以后的合作哦！''',
                    'from': '<EMAIL>'
                },
                'expected': 'rejection',
                'should_recall': True,
                'reason': '明确要求"自行撤稿"，虽然期待未来合作但当前拒绝'
            },
            {
                'name': '对比案例：无虞文化 - 临时暂停（不应撤回）',
                'email': {
                    'subject': '感谢投稿',
                    'body': '''宝宝好，这里是无虞文化的拉拉，感谢投稿~

暂停收稿两天哦，12号和13号的稿子不审了哦，周六日的稿子会在周一上班审稿哦~不一个个回复了哦

无虞主收追妻火葬场、下沉世情（小程序黑岩风，强情绪快节奏）、悬疑猎奇、热点事件文......除不收的类型外基本都收的哦~''',
                    'from': '<EMAIL>'
                },
                'expected': 'business_notice',
                'should_recall': False,
                'reason': '有明确恢复时间和收稿方向说明，是业务通知'
            },
            {
                'name': '对比案例：晴鲤文化 - 收稿确认（不应撤回）',
                'email': {
                    'subject': '投稿确认',
                    'body': '''你好呀~这里是晴鲤文化阿鲤，你的投稿已经收到啦，阿鲤正在加速审核哦！

晴鲤文化主收【知乎风】，千50，一审可立结，审核期1-3日。

如3日内未收到回复，那就是漏掉啦，速速加qq3842911634哦！''',
                    'from': '<EMAIL>'
                },
                'expected': 'auto_reply',
                'should_recall': False,
                'reason': '确认收稿并说明审核流程，积极合作'
            }
        ]
        
        # 执行测试
        results = []
        logger.info("\n" + "="*80)
        
        for i, test_case in enumerate(new_cases, 1):
            logger.info(f"\n📝 测试 {i}: {test_case['name']}")
            logger.info("-" * 60)
            logger.info(f"📄 邮件内容预览: {test_case['email']['body'][:100]}...")
            logger.info(f"🎯 期望结果: {test_case['expected']} (撤回: {test_case['should_recall']})")
            logger.info(f"💡 判断依据: {test_case['reason']}")
            
            # 进行智能识别
            is_auto, reply_type = receiver.is_auto_reply(test_case['email'])
            should_recall = reply_type == 'rejection'
            
            # 判断结果
            type_correct = reply_type == test_case['expected']
            recall_correct = should_recall == test_case['should_recall']
            overall_correct = type_correct and recall_correct
            
            result = {
                'test_name': test_case['name'],
                'detected_type': reply_type,
                'expected_type': test_case['expected'],
                'should_recall': should_recall,
                'expected_recall': test_case['should_recall'],
                'type_correct': type_correct,
                'recall_correct': recall_correct,
                'overall_correct': overall_correct
            }
            results.append(result)
            
            # 显示结果
            status = "✅" if overall_correct else "❌"
            logger.info(f"\n{status} 检测结果:")
            logger.info(f"   识别类型: {reply_type} (期望: {test_case['expected']})")
            logger.info(f"   是否撤回: {should_recall} (期望: {test_case['should_recall']})")
            
            if overall_correct:
                logger.info(f"   🎉 判断正确！")
            else:
                logger.warning(f"   ⚠️ 判断错误！")
                if not type_correct:
                    logger.warning(f"      类型识别错误")
                if not recall_correct:
                    logger.warning(f"      撤回判断错误")
        
        # 统计结果
        logger.info("\n" + "="*80)
        logger.info("📊 测试结果统计:")
        
        total_tests = len(results)
        correct_tests = sum(1 for r in results if r['overall_correct'])
        type_accuracy = sum(1 for r in results if r['type_correct']) / total_tests * 100
        recall_accuracy = sum(1 for r in results if r['recall_correct']) / total_tests * 100
        overall_accuracy = correct_tests / total_tests * 100
        
        logger.info(f"   总测试数: {total_tests}")
        logger.info(f"   完全正确: {correct_tests}")
        logger.info(f"   类型识别准确率: {type_accuracy:.1f}%")
        logger.info(f"   撤回判断准确率: {recall_accuracy:.1f}%")
        logger.info(f"   整体准确率: {overall_accuracy:.1f}%")
        
        # 重点分析新案例
        logger.info("\n🎯 新案例分析:")
        new_case_results = results[:2]  # 前两个是新案例
        
        for result in new_case_results:
            status = "✅ 正确" if result['overall_correct'] else "❌ 错误"
            logger.info(f"   {result['test_name']}: {status}")
            logger.info(f"      检测: {result['detected_type']} | 撤回: {result['should_recall']}")
        
        # 对比分析
        logger.info("\n🔍 对比分析:")
        logger.info("   新案例特点：温和表达但明确要求撤回")
        logger.info("   对比案例特点：暂停但有恢复计划和合作意愿")
        logger.info("   关键区别：是否要求撤回/撤稿")
        
        if all(r['overall_correct'] for r in new_case_results):
            logger.info("\n🎉 新案例全部识别正确！")
            logger.info("   系统能够准确识别温和表达的拒绝")
            logger.info("   深度语义分析功能正常工作")
        else:
            logger.warning("\n⚠️ 新案例识别有误，需要优化算法")
        
        return overall_accuracy >= 75  # 75%以上准确率算通过
        
    except Exception as e:
        logger.error(f"❌ 新案例测试失败: {str(e)}")
        return False

def test_semantic_patterns():
    """测试语义模式识别"""
    logger.info("\n🔍 测试语义模式识别...")
    
    try:
        from email_receiver import EmailReceiver
        receiver = EmailReceiver("<EMAIL>", "test_password")
        
        # 语义模式测试
        semantic_tests = [
            {
                'text': '我不会打开文件，咱们这边及时撤回就好',
                'expected': 'rejection',
                'pattern': '不会处理 + 撤回要求'
            },
            {
                'text': '麻烦动动手指自行撤稿哦',
                'expected': 'rejection', 
                'pattern': '要求自行撤稿'
            },
            {
                'text': '祝宝子们短篇在别处大麦',
                'expected': 'rejection',
                'pattern': '建议去别处'
            },
            {
                'text': '暂停收稿两天，周一恢复审稿',
                'expected': 'business_notice',
                'pattern': '临时暂停 + 恢复计划'
            },
            {
                'text': '感谢投稿，正在审核中',
                'expected': 'auto_reply',
                'pattern': '确认收稿 + 处理中'
            }
        ]
        
        logger.info("🧪 语义模式测试:")
        for test in semantic_tests:
            email_content = {
                'subject': 'Test',
                'body': test['text'],
                'from': '<EMAIL>'
            }
            
            is_auto, reply_type = receiver.is_auto_reply(email_content)
            correct = reply_type == test['expected']
            status = "✅" if correct else "❌"
            
            logger.info(f"   {status} {test['pattern']}: {reply_type} (期望: {test['expected']})")
            logger.info(f"      文本: {test['text']}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 语义模式测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    logger.info("🚀 开始深度语义分析测试...")
    
    try:
        # 测试新案例
        new_cases_success = test_new_cases()
        
        # 测试语义模式
        semantic_success = test_semantic_patterns()
        
        if new_cases_success and semantic_success:
            logger.info("\n🎉 深度语义分析测试全部通过！")
            logger.info("✅ 系统现在能够:")
            logger.info("   🧠 通读全文进行语义理解")
            logger.info("   🎯 识别温和表达的拒绝")
            logger.info("   📋 区分业务通知和真正拒绝")
            logger.info("   🚫 准确判断是否需要撤回")
            return True
        else:
            logger.warning("\n⚠️ 部分测试未通过，需要进一步优化")
            return False
        
    except Exception as e:
        logger.error(f"❌ 测试执行失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试空邮件正文问题
验证系统是否会在空正文时添加默认内容
"""

import logging
import sys

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_empty_body_processing():
    """测试空正文处理"""
    logger.info("🧪 测试空邮件正文处理...")
    
    try:
        # 模拟GUI中的处理逻辑
        
        # 测试1：完全空的正文
        logger.info("\n📝 测试1：完全空的正文")
        body_raw = ""
        
        # 处理占位符文本（模拟GUI逻辑）
        placeholder_text = "请在此输入邮件正文内容...\n\n支持功能：\n• 支持中文输入\n• 支持Emoji表情 😊\n• 支持多行文本\n• 右键菜单快速插入表情"
        if body_raw == placeholder_text.strip():
            body = ""  # 如果是占位符文本，则视为空正文
        else:
            body = body_raw
        
        logger.info(f"原始正文: '{body_raw}'")
        logger.info(f"处理后正文: '{body}'")
        logger.info(f"正文长度: {len(body)}")
        
        # 测试个性化处理
        def _personalize_content(body, sequence, add_personalization=False):
            """模拟个性化内容处理"""
            if not body.strip():
                return body  # 空正文不做处理
            
            # 检查用户是否选择添加个性化后缀
            if not add_personalization:
                return body  # 用户未选择添加个性化，直接返回原始正文
            
            import datetime
            
            # 添加时间戳（精确到秒，确保每封邮件都不同）
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            # 在正文末尾添加轻微的个性化信息（不影响用户体验）
            personalized_body = body
            
            # 如果正文不为空且用户选择了个性化，添加个性化后缀
            if body.strip():
                # 使用序号和时间戳确保每封邮件都不同
                personalized_body += f"\n\n---\n邮件编号: #{sequence:03d} | 发送时间: {timestamp}"
            
            return personalized_body
        
        # 测试不启用个性化
        personalized_body_off = _personalize_content(body, 1, False)
        logger.info(f"个性化关闭后: '{personalized_body_off}'")
        logger.info(f"个性化关闭长度: {len(personalized_body_off)}")
        
        # 测试启用个性化
        personalized_body_on = _personalize_content(body, 1, True)
        logger.info(f"个性化开启后: '{personalized_body_on}'")
        logger.info(f"个性化开启长度: {len(personalized_body_on)}")
        
        # 测试2：只有空白字符的正文
        logger.info("\n📝 测试2：只有空白字符的正文")
        body_raw2 = "   \n\t  \n  "
        body2 = body_raw2.strip()
        
        logger.info(f"原始正文: '{body_raw2}'")
        logger.info(f"处理后正文: '{body2}'")
        logger.info(f"正文长度: {len(body2)}")
        
        personalized_body2_off = _personalize_content(body2, 1, False)
        personalized_body2_on = _personalize_content(body2, 1, True)
        
        logger.info(f"个性化关闭后: '{personalized_body2_off}'")
        logger.info(f"个性化开启后: '{personalized_body2_on}'")
        
        # 测试3：占位符文本
        logger.info("\n📝 测试3：占位符文本")
        body_raw3 = placeholder_text.strip()
        
        if body_raw3 == placeholder_text.strip():
            body3 = ""  # 如果是占位符文本，则视为空正文
        else:
            body3 = body_raw3
        
        logger.info(f"原始正文: '{body_raw3[:50]}...'")
        logger.info(f"处理后正文: '{body3}'")
        logger.info(f"正文长度: {len(body3)}")
        
        personalized_body3_off = _personalize_content(body3, 1, False)
        personalized_body3_on = _personalize_content(body3, 1, True)
        
        logger.info(f"个性化关闭后: '{personalized_body3_off}'")
        logger.info(f"个性化开启后: '{personalized_body3_on}'")
        
        # 测试4：有内容的正文
        logger.info("\n📝 测试4：有内容的正文")
        body_raw4 = "这是一封测试邮件"
        body4 = body_raw4.strip()
        
        logger.info(f"原始正文: '{body4}'")
        logger.info(f"正文长度: {len(body4)}")
        
        personalized_body4_off = _personalize_content(body4, 1, False)
        personalized_body4_on = _personalize_content(body4, 1, True)
        
        logger.info(f"个性化关闭后: '{personalized_body4_off}'")
        logger.info(f"个性化开启后: '{personalized_body4_on}'")
        
        # 总结
        logger.info("\n📊 测试总结:")
        logger.info("=" * 50)
        
        test_cases = [
            ("完全空正文", body, personalized_body_off, personalized_body_on),
            ("空白字符正文", body2, personalized_body2_off, personalized_body2_on),
            ("占位符文本", body3, personalized_body3_off, personalized_body3_on),
            ("有内容正文", body4, personalized_body4_off, personalized_body4_on)
        ]
        
        for name, original, off, on in test_cases:
            logger.info(f"{name}:")
            logger.info(f"  原始: '{original}' (长度: {len(original)})")
            logger.info(f"  个性化关闭: '{off}' (长度: {len(off)})")
            logger.info(f"  个性化开启: '{on}' (长度: {len(on)})")
            
            if len(original) == 0 and len(off) == 0:
                logger.info(f"  ✅ 空正文正确处理")
            elif len(original) == 0 and len(off) > 0:
                logger.warning(f"  ❌ 空正文被添加了内容！")
            else:
                logger.info(f"  ✅ 非空正文正常处理")
            logger.info("")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {str(e)}")
        return False

def test_email_sender_empty_body():
    """测试EmailSender对空正文的处理"""
    logger.info("\n🧪 测试EmailSender对空正文的处理...")
    
    try:
        from email_sender import EmailSender
        
        # 创建测试发送器
        sender = EmailSender("<EMAIL>")
        
        # 测试创建空正文邮件
        test_emails = [
            ("", "空正文"),
            ("   ", "空白字符正文"),
            ("测试内容", "有内容正文")
        ]
        
        for body, description in test_emails:
            logger.info(f"\n📧 测试 {description}:")
            logger.info(f"输入正文: '{body}' (长度: {len(body)})")
            
            try:
                # 创建邮件消息
                msg = sender._create_message(
                    to_emails=["<EMAIL>"],
                    subject="测试邮件",
                    body=body
                )
                
                # 获取邮件正文
                email_body = None
                for part in msg.walk():
                    if part.get_content_type() == "text/plain":
                        email_body = part.get_payload(decode=True).decode('utf-8')
                        break
                
                logger.info(f"邮件正文: '{email_body}' (长度: {len(email_body) if email_body else 0})")
                
                if body.strip() == "" and email_body == "":
                    logger.info("✅ 空正文正确处理")
                elif body.strip() == "" and email_body != "":
                    logger.warning("❌ 空正文被添加了默认内容！")
                else:
                    logger.info("✅ 非空正文正常处理")
                
            except Exception as e:
                logger.error(f"❌ 创建邮件失败: {str(e)}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ EmailSender测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    logger.info("🚀 开始空邮件正文测试...")
    
    try:
        # 测试GUI处理逻辑
        gui_test_success = test_empty_body_processing()
        
        # 测试EmailSender处理逻辑
        sender_test_success = test_email_sender_empty_body()
        
        if gui_test_success and sender_test_success:
            logger.info("\n🎉 所有测试通过！")
            logger.info("✅ 系统正确处理空邮件正文")
            logger.info("✅ 不会添加不必要的默认内容")
            return True
        else:
            logger.warning("\n⚠️ 部分测试失败")
            return False
        
    except Exception as e:
        logger.error(f"❌ 测试执行失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

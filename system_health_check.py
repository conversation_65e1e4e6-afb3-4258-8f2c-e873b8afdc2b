#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统健康检查
定期检查系统状态和潜在问题
"""

import os
import sys
import ast
import logging
import subprocess
from datetime import datetime

def check_syntax(file_path):
    """检查文件语法"""
    try:
        result = subprocess.run([sys.executable, '-m', 'py_compile', file_path], 
                              capture_output=True, text=True)
        return result.returncode == 0, result.stderr
    except Exception as e:
        return False, str(e)

def check_imports(file_path):
    """检查导入依赖"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            tree = ast.parse(f.read())
        
        imports = []
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                imports.append(node.module)
        
        missing_imports = []
        for imp in imports:
            if imp:
                try:
                    __import__(imp)
                except ImportError:
                    missing_imports.append(imp)
        
        return len(missing_imports) == 0, missing_imports
    except Exception as e:
        return False, [str(e)]

def health_check():
    """执行健康检查"""
    print("🏥 系统健康检查")
    print("=" * 50)
    
    # 检查主文件
    files_to_check = ['gui_main.py', 'email_sender.py', 'empty_body_validator.py']
    
    all_healthy = True
    
    for file in files_to_check:
        if os.path.exists(file):
            print(f"\n📄 检查文件: {file}")
            
            # 语法检查
            syntax_ok, syntax_error = check_syntax(file)
            if syntax_ok:
                print("  ✅ 语法检查: 通过")
            else:
                print(f"  ❌ 语法检查: 失败 - {syntax_error}")
                all_healthy = False
            
            # 导入检查
            import_ok, missing = check_imports(file)
            if import_ok:
                print("  ✅ 导入检查: 通过")
            else:
                print(f"  ❌ 导入检查: 缺少 {missing}")
                all_healthy = False
        else:
            print(f"\n❌ 文件不存在: {file}")
            all_healthy = False
    
    print("\n" + "=" * 50)
    if all_healthy:
        print("🎉 系统健康状态: 良好")
    else:
        print("⚠️ 系统健康状态: 需要修复")
    
    return all_healthy

if __name__ == "__main__":
    health_check()
